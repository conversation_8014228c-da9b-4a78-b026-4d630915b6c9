console.log('🚀 Démarrage du serveur simple-login...');

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

console.log('✅ Middleware configuré');

// Route de test
app.get('/', (req, res) => {
  console.log('📡 GET /');
  res.json({ 
    message: 'Serveur simple-login OK', 
    timestamp: new Date().toISOString(),
    status: 'running'
  });
});

// Route de login
app.post('/login', (req, res) => {
  console.log('📥 POST /login reçu:', req.body);
  const { email, motDepass } = req.body;

  // Validation
  if (!email || !motDepass) {
    console.log('❌ Champs manquants');
    return res.status(400).json({
      success: false,
      message: "Email et mot de passe requis"
    });
  }

  // Test technicien
  if (email === '<EMAIL>' && motDepass === 'Tech123') {
    console.log('✅ Connexion technicien réussie');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      }
    });
  }

  // Test admin
  if (email === '<EMAIL>' && motDepass === 'admin123') {
    console.log('✅ Connexion admin réussie');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      }
    });
  }

  // Échec
  console.log('❌ Identifiants incorrects');
  return res.status(401).json({
    success: false,
    message: 'Email ou mot de passe incorrect'
  });
});

// Routes API pour la consommation
app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 GET /api/last-consommation-global');
  res.json({
    success: true,
    message: 'Dernière consommation récupérée',
    data: {
      idcons: 18,
      consommationpre: 122,
      consommationactuelle: 135,
      idcont: 10,
      jours: 30,
      periode: "juin 2025"
    }
  });
});

app.get('/api/contracts', (req, res) => {
  console.log('📥 GET /api/contracts');
  res.json({
    success: true,
    message: 'Contrats récupérés',
    data: [{
      idcontract: 10,
      codeqr: "QR123",
      nom: "Dupont",
      prenom: "Jean",
      ville: "Paris"
    }]
  });
});

app.post('/api/consommations', (req, res) => {
  console.log('📝 POST /api/consommations:', req.body);
  res.json({
    success: true,
    message: 'Consommation enregistrée avec succès',
    data: {
      id: Math.floor(Math.random() * 1000),
      ...req.body,
      timestamp: new Date().toISOString()
    }
  });
});

console.log('✅ Routes configurées');

// Démarrage
app.listen(PORT, () => {
  console.log(`✅ Serveur simple-login démarré sur http://localhost:${PORT}`);
  console.log('🔑 Comptes disponibles:');
  console.log('  - <EMAIL> / Tech123');
  console.log('  - <EMAIL> / admin123');
  console.log('📡 Routes disponibles:');
  console.log('  - POST /login');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
  console.log('🎯 Serveur prêt !');
});

// Gestion des erreurs
app.on('error', (error) => {
  console.error('❌ Erreur serveur:', error);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Exception:', error);
});
