console.log('🚀 Démarrage serveur HTTP basique...');

const http = require('http');
const url = require('url');

const PORT = 3003;

// Fonction pour parser le JSON du body
function parseBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const parsed = body ? JSON.parse(body) : {};
      callback(null, parsed);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Serveur HTTP
const server = http.createServer((req, res) => {
  // Headers CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  // Gestion OPTIONS (preflight)
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`📡 ${method} ${path}`);

  // Route GET /
  if (method === 'GET' && path === '/') {
    res.writeHead(200);
    res.end(JSON.stringify({
      message: 'Serveur HTTP basique OK',
      timestamp: new Date().toISOString(),
      status: 'running'
    }));
    return;
  }

  // Route POST /login
  if (method === 'POST' && path === '/login') {
    parseBody(req, (error, body) => {
      if (error) {
        console.error('❌ Erreur parsing body:', error);
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Erreur parsing JSON' }));
        return;
      }

      console.log('📥 Login data:', body);
      const { email, motDepass } = body;

      if (!email || !motDepass) {
        res.writeHead(400);
        res.end(JSON.stringify({
          success: false,
          message: 'Email et mot de passe requis'
        }));
        return;
      }

      // Test technicien
      if (email === '<EMAIL>' && motDepass === 'Tech123') {
        console.log('✅ Connexion technicien réussie');
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Connexion réussie',
          user: {
            id: 1,
            nom: 'Technicien',
            prenom: 'Test',
            email: '<EMAIL>',
            role: 'Tech'
          }
        }));
        return;
      }

      // Test admin
      if (email === '<EMAIL>' && motDepass === 'admin123') {
        console.log('✅ Connexion admin réussie');
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: 'Connexion réussie',
          user: {
            id: 2,
            nom: 'Admin',
            prenom: 'Test',
            email: '<EMAIL>',
            role: 'Admin'
          }
        }));
        return;
      }

      // Échec
      console.log('❌ Identifiants incorrects');
      res.writeHead(401);
      res.end(JSON.stringify({
        success: false,
        message: 'Email ou mot de passe incorrect'
      }));
    });
    return;
  }

  // Route GET /api/last-consommation-global
  if (method === 'GET' && path === '/api/last-consommation-global') {
    console.log('📥 GET last-consommation-global');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: {
        idcons: 18,
        consommationpre: 122,
        consommationactuelle: 135,
        idcont: 10,
        jours: 30,
        periode: "juin 2025"
      }
    }));
    return;
  }

  // Route GET /api/contracts
  if (method === 'GET' && path === '/api/contracts') {
    console.log('📥 GET contracts');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      data: [{
        idcontract: 10,
        codeqr: "QR123",
        nom: "Dupont",
        prenom: "Jean",
        ville: "Paris"
      }]
    }));
    return;
  }

  // Route POST /api/consommations
  if (method === 'POST' && path === '/api/consommations') {
    parseBody(req, (error, body) => {
      if (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: 'Erreur parsing JSON' }));
        return;
      }

      console.log('📝 POST consommations:', body);
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        message: 'Consommation enregistrée avec succès',
        data: {
          id: Math.floor(Math.random() * 1000),
          ...body,
          timestamp: new Date().toISOString()
        }
      }));
    });
    return;
  }

  // Route GET /api/table/client
  if (method === 'GET' && path === '/api/table/client') {
    console.log('📥 GET table/client');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Clients récupérés',
      data: [
        { idclient: 1, nom: "Dupont", prenom: "Jean", ville: "Paris", telephone: "0123456789" },
        { idclient: 2, nom: "Martin", prenom: "Marie", ville: "Lyon", telephone: "0987654321" },
        { idclient: 3, nom: "Bernard", prenom: "Paul", ville: "Marseille", telephone: "0147258369" }
      ]
    }));
    return;
  }

  // Route GET /api/clients (pour la page listes_clients.js)
  if (method === 'GET' && path === '/api/clients') {
    console.log('📥 GET clients (pour listes_clients.js)');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Clients récupérés depuis la base Facutration',
      data: [
        {
          idclient: 1,
          nom: "Dupont",
          prenom: "Jean",
          ville: "Paris",
          telephone: "0123456789",
          adresse: "123 Rue de la Paix",
          email: "<EMAIL>"
        },
        {
          idclient: 2,
          nom: "Martin",
          prenom: "Marie",
          ville: "Lyon",
          telephone: "0987654321",
          adresse: "456 Avenue de la République",
          email: "<EMAIL>"
        },
        {
          idclient: 3,
          nom: "Bernard",
          prenom: "Paul",
          ville: "Marseille",
          telephone: "0147258369",
          adresse: "789 Boulevard des Capucines",
          email: "<EMAIL>"
        },
        {
          idclient: 4,
          nom: "Durand",
          prenom: "Sophie",
          ville: "Toulouse",
          telephone: "0156789012",
          adresse: "321 Rue Victor Hugo",
          email: "<EMAIL>"
        },
        {
          idclient: 5,
          nom: "Moreau",
          prenom: "Pierre",
          ville: "Nice",
          telephone: "0198765432",
          adresse: "654 Promenade des Anglais",
          email: "<EMAIL>"
        }
      ]
    }));
    return;
  }

  // Route GET /api/table/consommation
  if (method === 'GET' && path === '/api/table/consommation') {
    console.log('📥 GET table/consommation');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Consommations récupérées',
      data: [
        { idcons: 1, consommationpre: 100, consommationactuelle: 120, idcont: 1, periode: "mai 2025" },
        { idcons: 2, consommationpre: 120, consommationactuelle: 135, idcont: 2, periode: "juin 2025" }
      ]
    }));
    return;
  }

  // Route GET /api/table/contract
  if (method === 'GET' && path === '/api/table/contract') {
    console.log('📥 GET table/contract');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Contrats récupérés',
      data: [
        { idcontract: 1, codeqr: "QR001", datecontract: "2024-01-15", marquecompteur: "Sagemcom" },
        { idcontract: 2, codeqr: "QR002", datecontract: "2024-02-20", marquecompteur: "Itron" }
      ]
    }));
    return;
  }

  // Route GET /api/technician/dashboard/:id
  if (method === 'GET' && path.startsWith('/api/technician/dashboard/')) {
    const techId = path.split('/').pop();
    console.log(`📥 GET technician/dashboard/${techId}`);
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'Dashboard technicien récupéré',
      data: {
        statistiques: {
          total_clients: 15,
          consommations_ce_mois: 8,
          factures_generees: 12,
          dernieres_consommations: [
            { idcons: 1, client: "Dupont Jean", consommation: 120, date: "2025-06-25" },
            { idcons: 2, client: "Martin Marie", consommation: 135, date: "2025-06-24" }
          ]
        }
      }
    }));
    return;
  }

  // Route GET /api/scan/:qrcode
  if (method === 'GET' && path.startsWith('/api/scan/')) {
    const qrCode = path.split('/').pop();
    console.log(`📥 GET scan/${qrCode}`);
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: 'QR Code scanné avec succès',
      data: {
        qrcode: qrCode,
        client: {
          idclient: 1,
          nom: "Dupont",
          prenom: "Jean",
          adresse: "123 Rue de la Paix, Paris"
        },
        contrat: {
          idcontract: 1,
          codeqr: qrCode,
          marquecompteur: "Sagemcom"
        }
      }
    }));
    return;
  }

  // Route non trouvée
  res.writeHead(404);
  res.end(JSON.stringify({ message: 'Route non trouvée' }));
});

// Démarrage du serveur
server.listen(PORT, () => {
  console.log(`✅ Serveur HTTP basique démarré sur http://localhost:${PORT}`);
  console.log('🔑 Comptes disponibles:');
  console.log('  - <EMAIL> / Tech123');
  console.log('  - <EMAIL> / admin123');
  console.log('📡 Routes disponibles:');
  console.log('  - GET /');
  console.log('  - POST /login');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
  console.log('🎯 Serveur prêt !');
});

server.on('error', (error) => {
  console.error('❌ Erreur serveur:', error);
});
