const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3007;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur de correction...');

// Route de test
app.get('/', (req, res) => {
  console.log('📥 Test du serveur');
  res.json({ 
    message: 'Serveur de correction fonctionnel', 
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route pour récupérer la dernière consommation (simulation temporaire)
app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 Récupération de la dernière consommation...');
  
  res.json({
    success: true,
    message: 'Dernière consommation récupérée avec succès',
    data: {
      idcons: 18,
      consommationpre: 122,
      consommationactuelle: 135, // Dernière consommation de votre base
      idcont: 10,
      idtech: 1,
      idtranch: 1,
      jours: 30,
      periode: "juin 2025",
      status: "nouveau"
    }
  });
});

// Route pour récupérer les contrats (simulation temporaire)
app.get('/api/contracts', (req, res) => {
  console.log('📥 Récupération des contrats...');
  
  res.json({
    success: true,
    message: 'Contrats récupérés avec succès',
    count: 1,
    data: [{
      idcontract: 10,
      codeqr: "QR123",
      datecontract: "2024-01-15T09:00:00.000Z",
      idclient: 3,
      marquecompteur: "Sagemcom",
      nom: "Dupont",
      prenom: "Jean",
      ville: "Paris"
    }]
  });
});

// Route POST pour enregistrer une consommation
app.post('/api/consommations', (req, res) => {
  console.log('📝 Enregistrement d\'une nouvelle consommation...');
  console.log('Données reçues:', req.body);
  
  try {
    // Simulation d'un succès
    const newConsommation = {
      idcons: Math.floor(Math.random() * 1000) + 100,
      consommationpre: req.body.consommationpre || 0,
      consommationactuelle: req.body.consommationactuelle,
      idcont: req.body.idcont,
      idtech: req.body.idtech || 1,
      idtranch: 1,
      jours: req.body.jours || 30,
      periode: req.body.periode || new Date().toISOString().slice(0, 7),
      status: 'nouveau',
      timestamp: new Date().toISOString()
    };
    
    console.log('✅ Consommation simulée enregistrée:', newConsommation);
    
    res.json({
      success: true,
      message: 'Consommation enregistrée avec succès',
      data: newConsommation
    });
  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement',
      error: error.message
    });
  }
});

// Démarrage du serveur avec gestion d'erreurs
const server = app.listen(PORT, () => {
  console.log(`✅ Serveur de correction démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
});

server.on('error', (error) => {
  console.error('❌ Erreur du serveur:', error);
  if (error.code === 'EADDRINUSE') {
    console.log('⚠️ Le port 3006 est déjà utilisé. Essayez de fermer les autres serveurs.');
  }
});

// Gestion des erreurs globales
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
