const express = require('express');
const cors = require('cors');

const app = express();

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
  res.json({ message: 'OK' });
});

app.get('/api/last-consommation-global', (req, res) => {
  res.json({
    success: true,
    data: { consommationactuelle: 135 }
  });
});

app.get('/api/contracts', (req, res) => {
  res.json({
    success: true,
    data: [{ idcontract: 10, nom: "Dupont" }]
  });
});

app.post('/api/consommations', (req, res) => {
  console.log('POST /api/consommations reçu');
  console.log('Body:', req.body);
  res.json({
    success: true,
    message: 'Consommation enregistrée avec succès',
    data: { id: 123 }
  });
});

app.listen(9999, () => {
  console.log('Serveur démarré sur port 9999');
});
