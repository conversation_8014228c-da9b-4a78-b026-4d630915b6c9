console.log('🚀 Démarrage du serveur basique...');

try {
  const express = require('express');
  console.log('✅ Express chargé');
  
  const cors = require('cors');
  console.log('✅ CORS chargé');
  
  const app = express();
  console.log('✅ App Express créée');
  
  // Middleware
  app.use(cors());
  console.log('✅ CORS configuré');
  
  app.use(express.json());
  console.log('✅ JSON middleware configuré');
  
  // Route de test simple
  app.get('/', (req, res) => {
    console.log('📡 Route / appelée');
    res.json({
      message: 'Serveur basique fonctionnel',
      timestamp: new Date().toISOString(),
      status: 'OK'
    });
  });
  console.log('✅ Route / configurée');
  
  // Route pour récupérer la dernière consommation
  app.get('/api/last-consommation-global', (req, res) => {
    console.log('📥 GET /api/last-consommation-global');
    res.json({
      success: true,
      message: 'Dernière consommation récupérée avec succès',
      data: {
        idcons: 18,
        consommationpre: 122,
        consommationactuelle: 135,
        idcont: 10,
        idtech: 1,
        idtranch: 1,
        jours: 30,
        periode: "juin 2025",
        status: "nouveau"
      }
    });
  });
  console.log('✅ Route last-consommation-global configurée');
  
  // Route pour récupérer les contrats
  app.get('/api/contracts', (req, res) => {
    console.log('📥 GET /api/contracts');
    res.json({
      success: true,
      message: 'Contrats récupérés avec succès',
      count: 1,
      data: [{
        idcontract: 10,
        codeqr: "QR123",
        datecontract: "2024-01-15T09:00:00.000Z",
        idclient: 3,
        marquecompteur: "Sagemcom",
        nom: "Dupont",
        prenom: "Jean",
        ville: "Paris"
      }]
    });
  });
  console.log('✅ Route contracts configurée');
  
  // Route POST pour enregistrer une consommation
  app.post('/api/consommations', (req, res) => {
    console.log('📝 POST /api/consommations');
    console.log('Données reçues:', req.body);
    
    const newConsommation = {
      idcons: Math.floor(Math.random() * 1000) + 100,
      consommationpre: req.body.consommationpre || 0,
      consommationactuelle: req.body.consommationactuelle,
      idcont: req.body.idcont,
      idtech: req.body.idtech || 1,
      idtranch: 1,
      jours: req.body.jours || 30,
      periode: req.body.periode || new Date().toISOString().slice(0, 7),
      status: 'nouveau',
      timestamp: new Date().toISOString()
    };
    
    console.log('✅ Consommation simulée:', newConsommation);
    
    res.json({
      success: true,
      message: 'Consommation enregistrée avec succès',
      data: newConsommation
    });
  });
  console.log('✅ Route POST consommations configurée');
  
  // Démarrage du serveur
  const PORT = 8080;
  console.log(`🔧 Tentative de démarrage sur le port ${PORT}...`);
  
  const server = app.listen(PORT, () => {
    console.log(`✅ Serveur basique démarré avec succès sur http://localhost:${PORT}`);
    console.log('📡 Routes disponibles:');
    console.log('  - GET / (test)');
    console.log('  - GET /api/last-consommation-global');
    console.log('  - GET /api/contracts');
    console.log('  - POST /api/consommations');
    console.log('🎯 Le serveur est prêt à recevoir des requêtes !');
  });
  
  server.on('error', (error) => {
    console.error('❌ Erreur du serveur:', error);
    if (error.code === 'EADDRINUSE') {
      console.error(`❌ Le port ${PORT} est déjà utilisé`);
    }
  });
  
} catch (error) {
  console.error('❌ Erreur lors du démarrage:', error);
}

// Gestion des erreurs globales
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
