import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import axios from 'axios';

// Import des composants
import TechnicianDashboard from './TechnicianDashboard';
import Dashboard from './components/Dashboard';
import ListesClients from './listes_clients';
import TestPage from './TestPage';

// Import des pages du dashboard
import OverviewPage from './pages/OverviewPage';
import ConsommationPage from './pages/ConsommationPage';
import FacturesPage from './pages/FacturesPage';
import ScannerPage from './pages/ScannerPage';
import MapPage from './pages/MapPage';
import HistoriquePage from './pages/HistoriquePage';
import ProfilePage from './pages/ProfilePage';

import './LoginPage.css';

function AppRouter() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState(null);

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');

    console.log('Tentative de connexion avec:', { email, password });

    try {
      const response = await axios.post('http://localhost:3002/login', {
        email,
        motDepass: password
      });

      console.log('Réponse du serveur:', response.data);

      if (response.data.success) {
        setUserData(response.data.user);
        setIsLoggedIn(true);
        console.log('Connexion réussie pour:', response.data.user.email);
      } else {
        setError(response.data.message || 'Échec de la connexion');
      }

    } catch (err) {
      console.error('Erreur de connexion:', err);

      // Mode offline - vérification locale des identifiants
      console.log('🔄 Mode offline activé pour la connexion');

      if (email === '<EMAIL>' && password === 'Tech123') {
        console.log('✅ Connexion offline réussie');
        setUserData({
          idtech: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Tech'
        });
        setIsLoggedIn(true);
      } else {
        if (err.response) {
          setError(err.response.data?.message || 'Identifiants incorrects');
        } else if (err.request) {
          setError('Email ou mot de passe incorrect (Mode offline)');
        } else {
          setError('Email ou mot de passe incorrect (Mode offline)');
        }
      }
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUserData(null);
    setEmail('');
    setPassword('');
    console.log('Déconnexion effectuée');
  };

  // Composant de page de connexion
  const LoginPage = () => (
    <div className="login-container">
      <div className="login-card">
        <div className="card-header">
          <h1 className="login-title">Connexion</h1>
          <p className="login-subtitle">Système de Facturation</p>
        </div>

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <form onSubmit={handleLogin} className="login-form">
          <div className="input-group">
            <label className="input-label">Adresse Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="login-input"
              placeholder="<EMAIL>"
            />
          </div>

          <div className="input-group">
            <label className="input-label">Mot de Passe</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="login-input"
              placeholder="••••••••"
            />
          </div>

          <button
            type="submit"
            className="submit-button"
          >
            Se Connecter
          </button>
        </form>
      </div>
    </div>
  );

  // Composant de protection des routes
  const ProtectedRoute = ({ children }) => {
    return isLoggedIn ? children : <Navigate to="/login" replace />;
  };

  return (
    <Router>
      <Routes>
        {/* Route de connexion */}
        <Route 
          path="/login" 
          element={!isLoggedIn ? <LoginPage /> : <Navigate to="/dashboard" replace />} 
        />
        
        {/* Route par défaut - redirection vers login */}
        <Route 
          path="/" 
          element={<Navigate to="/login" replace />} 
        />

        {/* Routes protégées */}
        <Route 
          path="/dashboard" 
          element={
            <ProtectedRoute>
              {userData?.role === 'technicien' ? (
                <TechnicianDashboard user={userData} onLogout={handleLogout} />
              ) : (
                <Dashboard user={userData} onLogout={handleLogout} />
              )}
            </ProtectedRoute>
          } 
        />

        {/* Routes des pages du technician dashboard */}
        <Route 
          path="/listes-clients" 
          element={
            <ProtectedRoute>
              <ListesClients />
            </ProtectedRoute>
          } 
        />
        
        <Route
          path="/consommation"
          element={
            <ProtectedRoute>
              <ConsommationPage
                user={userData}
                onBack={() => window.location.href = '/dashboard'}
              />
            </ProtectedRoute>
          }
        />

        <Route
          path="/factures"
          element={
            <ProtectedRoute>
              <FacturesPage
                user={userData}
                onBack={() => window.location.href = '/dashboard'}
              />
            </ProtectedRoute>
          }
        />

        <Route
          path="/scanner"
          element={
            <ProtectedRoute>
              <ScannerPage
                user={userData}
                onBack={() => window.location.href = '/dashboard'}
              />
            </ProtectedRoute>
          }
        />

        <Route
          path="/map"
          element={
            <ProtectedRoute>
              <MapPage
                user={userData}
                onBack={() => window.location.href = '/dashboard'}
              />
            </ProtectedRoute>
          }
        />

        <Route
          path="/historique"
          element={
            <ProtectedRoute>
              <HistoriquePage
                user={userData}
                onBack={() => window.location.href = '/dashboard'}
              />
            </ProtectedRoute>
          }
        />

        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <ProfilePage
                user={userData}
                onLogout={handleLogout}
                onBack={() => window.location.href = '/dashboard'}
              />
            </ProtectedRoute>
          }
        />

        {/* Route de test */}
        <Route 
          path="/test" 
          element={
            <ProtectedRoute>
              <TestPage />
            </ProtectedRoute>
          } 
        />
      </Routes>
    </Router>
  );
}

export default AppRouter;
