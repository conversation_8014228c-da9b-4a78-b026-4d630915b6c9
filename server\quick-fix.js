const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration', // Votre base de données
  password: '123456',
  port: 5432,
});

app.get('/', (req, res) => {
  res.json({ message: 'Serveur fonctionnel', status: 'OK' });
});

app.get('/api/last-consommation-global', async (req, res) => {
  try {
    console.log('📥 Récupération de la dernière consommation de la table consommation...');

    const query = 'SELECT * FROM consommation ORDER BY idcons DESC LIMIT 1';
    const result = await pool.query(query);

    if (result.rows.length > 0) {
      console.log('✅ Dernière consommation trouvée:', result.rows[0]);
      res.json({
        success: true,
        message: 'Dernière consommation récupérée avec succès',
        data: result.rows[0]
      });
    } else {
      console.log('⚠️ Aucune consommation trouvée');
      res.json({
        success: true,
        message: 'Aucune consommation trouvée',
        data: { consommationactuelle: 0 }
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la consommation',
      error: error.message
    });
  }
});

app.get('/api/contracts', async (req, res) => {
  try {
    console.log('📥 Récupération des contrats de la table contract...');

    const query = 'SELECT * FROM contract ORDER BY idcontract';
    const result = await pool.query(query);

    console.log(`✅ ${result.rows.length} contrats trouvés`);
    res.json({
      success: true,
      message: 'Contrats récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

app.post('/api/consommations', async (req, res) => {
  try {
    console.log('📝 Enregistrement dans la table consommation...');
    console.log('Données reçues:', req.body);

    const { idcont, consommationpre, consommationactuelle, jours, periode, idtech } = req.body;

    const query = `
      INSERT INTO consommation (
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      consommationpre || 0,
      consommationactuelle,
      idcont,
      idtech || 1,
      1, // idtranch par défaut
      jours || 30,
      periode || new Date().toISOString().slice(0, 7),
      'nouveau'
    ];

    const result = await pool.query(query, values);

    console.log('✅ Consommation enregistrée avec succès:', result.rows[0]);
    res.json({
      success: true,
      message: 'Consommation enregistrée avec succès dans la table consommation',
      data: result.rows[0]
    });
  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement de la consommation',
      error: error.message
    });
  }
});

app.listen(3006, () => {
  console.log('✅ Serveur démarré sur http://localhost:3006');
});
