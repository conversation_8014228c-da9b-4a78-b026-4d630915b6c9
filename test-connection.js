// Script de test pour vérifier la connectivité
const http = require('http');

console.log('🔍 Test de connectivité...');

// Test 1: Vérifier si le port 3002 est en écoute
const testPort = (port) => {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: port,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Port ${port} accessible - Status: ${res.statusCode}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ Port ${port} non accessible - Erreur: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ Port ${port} timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
};

// Test 2: Vérifier la connectivité avec fetch (simulation frontend)
const testFetch = async () => {
  try {
    console.log('🔍 Test avec fetch...');
    const response = await fetch('http://localhost:3002/');
    const data = await response.json();
    console.log('✅ Fetch réussi:', data);
    return true;
  } catch (error) {
    console.log('❌ Fetch échoué:', error.message);
    return false;
  }
};

// Exécuter les tests
async function runTests() {
  console.log('='.repeat(50));
  console.log('🧪 TESTS DE CONNECTIVITÉ');
  console.log('='.repeat(50));
  
  // Test des ports
  console.log('\n📡 Test des ports:');
  await testPort(3001); // React
  await testPort(3002); // Backend
  await testPort(3005); // Test
  await testPort(3010); // Test
  
  // Test fetch (nécessite que fetch soit disponible)
  console.log('\n🌐 Test fetch:');
  if (typeof fetch !== 'undefined') {
    await testFetch();
  } else {
    console.log('⚠️ Fetch non disponible dans cet environnement Node.js');
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Tests terminés');
  console.log('='.repeat(50));
}

runTests();
