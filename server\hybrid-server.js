console.log('🚀 Démarrage du serveur hybride (PostgreSQL + fallback)...');

const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

console.log('✅ Middleware configuré');

// Tentative de connexion PostgreSQL
let pool = null;
let useDatabase = false;

try {
  const { Pool } = require('pg');
  
  pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'Facutration',
    password: '123456',
    port: 5432,
  });

  // Test de connexion
  pool.connect((err, client, release) => {
    if (err) {
      console.error('❌ PostgreSQL non disponible:', err.message);
      console.log('🔄 Utilisation des données statiques...');
      useDatabase = false;
    } else {
      console.log('✅ Connexion PostgreSQL réussie à la base "Facutration"');
      useDatabase = true;
      release();
    }
  });

} catch (error) {
  console.error('❌ Module pg non disponible:', error.message);
  console.log('🔄 Utilisation des données statiques...');
  useDatabase = false;
}

// Données statiques de fallback
const staticClients = [
  { 
    idclient: 1, 
    nom: "Dupont", 
    prenom: "Jean", 
    ville: "Paris", 
    tel: "0123456789",
    adresse: "123 Rue de la Paix",
    email: "<EMAIL>"
  },
  { 
    idclient: 2, 
    nom: "Martin", 
    prenom: "Marie", 
    ville: "Lyon", 
    tel: "0987654321",
    adresse: "456 Avenue de la République",
    email: "<EMAIL>"
  },
  { 
    idclient: 3, 
    nom: "Bernard", 
    prenom: "Paul", 
    ville: "Marseille", 
    tel: "0147258369",
    adresse: "789 Boulevard des Capucines",
    email: "<EMAIL>"
  },
  { 
    idclient: 4, 
    nom: "Durand", 
    prenom: "Sophie", 
    ville: "Toulouse", 
    tel: "0156789012",
    adresse: "321 Rue Victor Hugo",
    email: "<EMAIL>"
  },
  { 
    idclient: 5, 
    nom: "Moreau", 
    prenom: "Pierre", 
    ville: "Nice", 
    tel: "0198765432",
    adresse: "654 Promenade des Anglais",
    email: "<EMAIL>"
  }
];

// Route de test
app.get('/', (req, res) => {
  console.log('📡 GET /');
  res.json({ 
    message: 'Serveur hybride OK', 
    timestamp: new Date().toISOString(),
    status: 'running',
    database: useDatabase ? 'PostgreSQL Facutration' : 'Données statiques'
  });
});

// Route de login
app.post('/login', (req, res) => {
  console.log('📥 POST /login reçu:', req.body);
  const { email, motDepass } = req.body;

  if (!email || !motDepass) {
    console.log('❌ Champs manquants');
    return res.status(400).json({
      success: false,
      message: "Email et mot de passe requis"
    });
  }

  if (email === '<EMAIL>' && motDepass === 'Tech123') {
    console.log('✅ Connexion technicien réussie');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      }
    });
  }

  if (email === '<EMAIL>' && motDepass === 'admin123') {
    console.log('✅ Connexion admin réussie');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      }
    });
  }

  console.log('❌ Identifiants incorrects');
  return res.status(401).json({
    success: false,
    message: 'Email ou mot de passe incorrect'
  });
});

// Route GET /api/clients (pour la page listes_clients.js)
app.get('/api/clients', async (req, res) => {
  console.log('📥 GET /api/clients');

  if (useDatabase && pool) {
    try {
      console.log('🔍 Récupération depuis PostgreSQL...');
      const result = await pool.query(`
        SELECT
          idclient,
          nom,
          prenom,
          adresse,
          ville,
          tel,
          email
        FROM client
        ORDER BY nom, prenom
      `);

      console.log(`✅ ${result.rows.length} clients récupérés depuis PostgreSQL`);
      res.json({
        success: true,
        message: 'Clients récupérés depuis la base Facutration',
        data: result.rows,
        source: 'PostgreSQL'
      });
      return;
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
      console.log('🔄 Fallback vers données statiques...');
    }
  }

  // Fallback vers données statiques
  console.log('📊 Utilisation des données statiques');
  res.json({
    success: true,
    message: 'Clients récupérés (données statiques)',
    data: staticClients,
    source: 'Statique'
  });
});

// Route GET /api/table/client (pour le dashboard)
app.get('/api/table/client', async (req, res) => {
  console.log('📥 GET /api/table/client');

  if (useDatabase && pool) {
    try {
      const result = await pool.query(`
        SELECT idclient, nom, prenom, ville, tel
        FROM client
        ORDER BY nom, prenom
      `);

      res.json({
        success: true,
        message: 'Clients récupérés',
        data: result.rows
      });
      return;
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
    }
  }

  res.json({
    success: true,
    message: 'Clients récupérés',
    data: staticClients.map(c => ({
      idclient: c.idclient,
      nom: c.nom,
      prenom: c.prenom,
      ville: c.ville,
      tel: c.tel
    }))
  });
});

// Route GET /api/table/consommation
app.get('/api/table/consommation', async (req, res) => {
  console.log('📥 GET /api/table/consommation');

  if (useDatabase && pool) {
    try {
      const result = await pool.query(`
        SELECT idcons, consommationpre, consommationactuelle, idcont, periode
        FROM consommation
        ORDER BY periode DESC
      `);

      res.json({
        success: true,
        message: 'Consommations récupérées',
        data: result.rows
      });
      return;
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
    }
  }

  res.json({
    success: true,
    message: 'Consommations récupérées',
    data: [
      { idcons: 1, consommationpre: 100, consommationactuelle: 120, idcont: 1, periode: "mai 2025" },
      { idcons: 2, consommationpre: 120, consommationactuelle: 135, idcont: 2, periode: "juin 2025" }
    ]
  });
});

// Route GET /api/table/contract
app.get('/api/table/contract', async (req, res) => {
  console.log('📥 GET /api/table/contract');

  if (useDatabase && pool) {
    try {
      const result = await pool.query(`
        SELECT idcontract, codeqr, datecontract, marquecompteur
        FROM contract
        ORDER BY datecontract DESC
      `);

      res.json({
        success: true,
        message: 'Contrats récupérés',
        data: result.rows
      });
      return;
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
    }
  }

  res.json({
    success: true,
    message: 'Contrats récupérés',
    data: [
      { idcontract: 1, codeqr: "QR001", datecontract: "2024-01-15", marquecompteur: "Sagemcom" },
      { idcontract: 2, codeqr: "QR002", datecontract: "2024-02-20", marquecompteur: "Itron" }
    ]
  });
});

// Route GET /api/technician/dashboard/:id
app.get('/api/technician/dashboard/:id', async (req, res) => {
  const techId = req.params.id;
  console.log(`📥 GET /api/technician/dashboard/${techId}`);

  res.json({
    success: true,
    message: 'Dashboard technicien récupéré',
    data: {
      statistiques: {
        total_clients: 15,
        consommations_ce_mois: 8,
        factures_generees: 12,
        dernieres_consommations: [
          { idcons: 1, client: "Dupont Jean", consommation: 120, date: "2025-06-25" },
          { idcons: 2, client: "Martin Marie", consommation: 135, date: "2025-06-24" }
        ]
      }
    }
  });
});

// Route GET /api/scan/:qrcode
app.get('/api/scan/:qrcode', async (req, res) => {
  const qrCode = req.params.qrcode;
  console.log(`📥 GET /api/scan/${qrCode}`);

  res.json({
    success: true,
    message: 'QR Code scanné avec succès',
    data: {
      qrcode: qrCode,
      client: {
        idclient: 1,
        nom: "Dupont",
        prenom: "Jean",
        adresse: "123 Rue de la Paix, Paris"
      },
      contrat: {
        idcontract: 1,
        codeqr: qrCode,
        marquecompteur: "Sagemcom"
      }
    }
  });
});

// Route GET /api/contracts (pour récupérer les contrats)
app.get('/api/contracts', async (req, res) => {
  console.log('📥 GET /api/contracts');

  if (useDatabase && pool) {
    try {
      const result = await pool.query(`
        SELECT
          c.idcontract,
          c.codeqr,
          c.datecontract,
          c.marquecompteur,
          c.idclient,
          cl.nom,
          cl.prenom
        FROM contract c
        LEFT JOIN client cl ON c.idclient = cl.idclient
        ORDER BY c.datecontract DESC
      `);

      console.log(`✅ ${result.rows.length} contrats récupérés depuis PostgreSQL`);
      res.json({
        success: true,
        message: 'Contrats récupérés depuis la base Facutration',
        data: result.rows,
        source: 'PostgreSQL'
      });
      return;
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
    }
  }

  // Fallback vers données statiques
  res.json({
    success: true,
    message: 'Contrats récupérés (données statiques)',
    data: [
      { idcontract: 1, codeqr: "QR001", datecontract: "2024-01-15", marquecompteur: "Sagemcom", idclient: 1, nom: "Dupont", prenom: "Jean" },
      { idcontract: 2, codeqr: "QR002", datecontract: "2024-02-20", marquecompteur: "Itron", idclient: 2, nom: "Martin", prenom: "Marie" }
    ],
    source: 'Statique'
  });
});

// Route GET /api/last-consommation-global (dernière consommation globale)
app.get('/api/last-consommation-global', async (req, res) => {
  console.log('📥 GET /api/last-consommation-global');

  if (useDatabase && pool) {
    try {
      const result = await pool.query(`
        SELECT
          idcons,
          consommationpre,
          consommationactuelle,
          idcont,
          periode,
          jours
        FROM consommation
        ORDER BY idcons DESC
        LIMIT 1
      `);

      if (result.rows.length > 0) {
        console.log('✅ Dernière consommation globale récupérée:', result.rows[0]);
        res.json({
          success: true,
          message: 'Dernière consommation globale récupérée',
          data: result.rows[0],
          source: 'PostgreSQL'
        });
        return;
      } else {
        console.log('ℹ️ Aucune consommation trouvée dans la base');
        res.json({
          success: false,
          message: 'Aucune consommation trouvée',
          data: null
        });
        return;
      }
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
    }
  }

  // Fallback vers données statiques
  res.json({
    success: true,
    message: 'Dernière consommation (données statiques)',
    data: { idcons: 1, consommationpre: 100, consommationactuelle: 120, idcont: 1, periode: "2025-06", jours: 30 },
    source: 'Statique'
  });
});

// Route GET /api/contracts/:id/last-consommation (dernière consommation d'un contrat spécifique)
app.get('/api/contracts/:id/last-consommation', async (req, res) => {
  const contractId = req.params.id;
  console.log(`📥 GET /api/contracts/${contractId}/last-consommation`);

  if (useDatabase && pool) {
    try {
      const result = await pool.query(`
        SELECT
          idcons,
          consommationpre,
          consommationactuelle,
          idcont,
          periode,
          jours
        FROM consommation
        WHERE idcont = $1
        ORDER BY idcons DESC
        LIMIT 1
      `, [contractId]);

      if (result.rows.length > 0) {
        console.log(`✅ Dernière consommation du contrat ${contractId} récupérée:`, result.rows[0]);
        res.json({
          success: true,
          message: `Dernière consommation du contrat ${contractId} récupérée`,
          data: result.rows[0],
          source: 'PostgreSQL'
        });
        return;
      } else {
        console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${contractId}`);
        res.json({
          success: false,
          message: `Aucune consommation trouvée pour le contrat ${contractId}`,
          data: null
        });
        return;
      }
    } catch (error) {
      console.error('❌ Erreur PostgreSQL:', error.message);
    }
  }

  // Fallback vers données statiques
  res.json({
    success: false,
    message: 'Aucune consommation trouvée pour ce contrat',
    data: null
  });
});

// Route POST /api/consommations
app.post('/api/consommations', async (req, res) => {
  console.log('📝 POST /api/consommations reçu');
  console.log('📋 Headers:', req.headers);
  console.log('📋 Body:', req.body);
  console.log('📋 Content-Type:', req.get('Content-Type'));

  if (useDatabase && pool) {
    try {
      const { idcont, consommationpre, consommationactuelle, jours, periode } = req.body;

      const result = await pool.query(`
        INSERT INTO consommation (consommationpre, consommationactuelle, idcont, jours, periode)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [consommationpre, consommationactuelle, idcont, jours, periode]);

      console.log('✅ Consommation enregistrée dans PostgreSQL:', result.rows[0]);
      res.json({
        success: true,
        message: 'Consommation enregistrée avec succès dans la base Facutration',
        data: result.rows[0],
        source: 'PostgreSQL'
      });
      return;
    } catch (error) {
      console.error('❌ Erreur lors de l\'enregistrement:', error.message);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'enregistrement: ' + error.message
      });
      return;
    }
  }

  // Fallback vers simulation
  res.json({
    success: true,
    message: 'Consommation enregistrée avec succès (simulation)',
    data: {
      id: Math.floor(Math.random() * 1000),
      ...req.body,
      timestamp: new Date().toISOString()
    },
    source: 'Simulation'
  });
});

// Démarrage
app.listen(PORT, () => {
  console.log(`✅ Serveur hybride démarré sur http://localhost:${PORT}`);
  console.log(`🔑 Comptes disponibles:`);
  console.log('  - <EMAIL> / Tech123');
  console.log('  - <EMAIL> / admin123');
  console.log('📡 Routes disponibles:');
  console.log('  - POST /login');
  console.log('  - GET /api/clients');
  console.log(`📊 Source de données: ${useDatabase ? 'PostgreSQL Facutration' : 'Données statiques'}`);
  console.log('🎯 Serveur prêt !');
});

app.on('error', (error) => {
  console.error('❌ Erreur serveur:', error);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Exception:', error);
});
