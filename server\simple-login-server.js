// Serveur de login simple pour résoudre l'erreur de connexion
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('📡 Requête GET / reçue');
  res.json({
    message: 'Serveur de login AquaTrack fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de login
app.post('/login', (req, res) => {
  console.log('🔐 Tentative de connexion...');
  console.log('📧 Body reçu:', req.body);
  
  const { email, motDepass } = req.body;

  // Validation des champs
  if (!email || !motDepass) {
    console.log('❌ Champs manquants');
    return res.status(400).json({
      success: false,
      message: "Email et mot de passe requis"
    });
  }

  // Vérification des identifiants technicien
  if (email === '<EMAIL>' && motDepass === 'Tech123') {
    console.log('✅ Connexion technicien réussie');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech',
        adresse: '123 Rue Tech',
        tel: '0123456789'
      }
    });
  }

  // Vérification des identifiants admin
  if (email === '<EMAIL>' && motDepass === 'admin123') {
    console.log('✅ Connexion admin réussie');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: 2,
        nom: 'Admin',
        prenom: 'System',
        email: '<EMAIL>',
        role: 'Admin',
        adresse: '456 Rue Admin',
        tel: '0987654321'
      }
    });
  }

  // Identifiants incorrects
  console.log('❌ Identifiants incorrects');
  return res.status(401).json({
    success: false,
    message: 'Email ou mot de passe incorrect'
  });
});

// Route pour les données clients (simulation)
app.get('/api/table/client', (req, res) => {
  console.log('📡 Requête GET /api/table/client');
  res.json({
    success: true,
    data: [
      { idclient: 1, nom: 'Dupont', prenom: 'Jean', adresse: '123 Rue de la Paix', ville: 'Paris', tel: '0123456789', email: '<EMAIL>', ids: 1 },
      { idclient: 2, nom: 'Martin', prenom: 'Marie', adresse: '456 Avenue des Champs', ville: 'Lyon', tel: '0987654321', email: '<EMAIL>', ids: 2 },
      { idclient: 3, nom: 'Bernard', prenom: 'Pierre', adresse: '789 Boulevard Central', ville: 'Marseille', tel: '0147258369', email: '<EMAIL>', ids: 1 }
    ],
    count: 3,
    message: 'Données clients (simulation)'
  });
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur'
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log('🚀 ================================');
  console.log(`🚀 Serveur de login démarré sur le port ${PORT}`);
  console.log('🚀 ================================');
  console.log(`📡 URL: http://localhost:${PORT}`);
  console.log('🔐 Identifiants de test:');
  console.log('   📧 <EMAIL> / Tech123');
  console.log('   📧 <EMAIL> / admin123');
  console.log('🚀 ================================');
});

// Gestion de l'arrêt propre
process.on('SIGINT', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Arrêt du serveur...');
  process.exit(0);
});
