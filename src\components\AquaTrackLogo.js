import React from 'react';

const AquaTrackLogo = ({ size = 50, showText = true }) => {
  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
      {/* Logo SVG */}
      <svg 
        width={size} 
        height={size} 
        viewBox="0 0 100 100" 
        style={{ flexShrink: 0 }}
      >
        {/* Cercle de fond */}
        <circle 
          cx="50" 
          cy="50" 
          r="45" 
          fill="#0066CC" 
          stroke="#004499" 
          strokeWidth="3"
        />
        
        {/* Goutte d'eau principale */}
        <path 
          d="M50 20 C40 35, 40 45, 50 55 C60 45, 60 35, 50 20 Z" 
          fill="#FFFFFF"
        />
        
        {/* Petit<PERSON> gouttes */}
        <circle cx="35" cy="40" r="4" fill="#87CEEB"/>
        <circle cx="65" cy="45" r="3" fill="#87CEEB"/>
        <circle cx="45" cy="65" r="2" fill="#87CEEB"/>
        
        {/* Vagues en bas */}
        <path 
          d="M15 70 Q25 65, 35 70 T55 70 T75 70 T85 70" 
          stroke="#FFFFFF" 
          strokeWidth="2" 
          fill="none"
        />
        <path 
          d="M20 75 Q30 72, 40 75 T60 75 T80 75" 
          stroke="#FFFFFF" 
          strokeWidth="1.5" 
          fill="none"
        />
      </svg>
      
      {/* Texte AquaTrack */}
      {showText && (
        <div>
          <div style={{ 
            fontSize: size > 40 ? '20px' : '16px',
            fontWeight: 'bold',
            color: '#0066CC',
            lineHeight: '1.2'
          }}>
            AquaTrack
          </div>
          <div style={{ 
            fontSize: size > 40 ? '12px' : '10px',
            color: '#666666',
            lineHeight: '1.2'
          }}>
            Système de Gestion d'Eau
          </div>
        </div>
      )}
    </div>
  );
};

export default AquaTrackLogo;
