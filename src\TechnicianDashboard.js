import React, { useState, useEffect } from 'react';
import './TechnicianDashboard.css';
import ListesClients from './listes_clients';
import OverviewPage from './pages/OverviewPage';
import ConsommationPage from './pages/ConsommationPage';
import FacturesPage from './pages/FacturesPage';
import ScannerPage from './pages/ScannerPage';
import MapPage from './pages/MapPage';
import HistoriquePage from './pages/HistoriquePage';
import ProfilePage from './pages/ProfilePage';
import ResultatsReleveePage from './pages/ResultatsReleveePage';
import SaisieClientPage from './pages/SaisieClientPage';
import AquaTrackLogo from './components/AquaTrackLogo';
const TechnicianDashboard = ({ user, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showListesClients, setShowListesClients] = useState(false);
  const [showResultatsReleve, setShowResultatsReleve] = useState(false);
  const [showSaisieClient, setShowSaisieClient] = useState(false);
  const [releveData, setReleveData] = useState(null);
  const [interventions, setInterventions] = useState([]);
  const [clients, setClients] = useState([]);
  const [consommations, setConsommations] = useState([]);
  const [factures, setFactures] = useState([]);
  const [historique, setHistorique] = useState([]);
  const [newConsommation, setNewConsommation] = useState({ clientId: '', valeur: '', date: '' });
  const [scannerActive, setScannerActive] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardStats, setDashboardStats] = useState({});

  // Configuration de l'API Backend
  const API_BASE_URL = 'http://localhost:3002';

  // Menu items pour la sidebar avec routes de navigation
  const menuItems = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: '📊', route: '/dashboard', type: 'internal' },
    { id: 'clients', label: 'Clients', icon: '👥', route: '/dashboard', type: 'internal' },
    { id: 'consommation', label: 'Consommation', icon: '💧', route: '/consommation', type: 'internal' },
    { id: 'factures', label: 'Factures', icon: '📄', route: '/factures', type: 'internal' },
    { id: 'scanner', label: 'Scanner QR', icon: '📱', route: '/scanner', type: 'internal' },
    { id: 'map', label: 'Localisation', icon: '🗺️', route: '/map', type: 'internal' },
    { id: 'historique', label: 'Historique', icon: '📋', route: '/historique', type: 'internal' },
    { id: 'profile', label: 'Profil', icon: '👤', route: '/profile', type: 'internal' }
  ];

  // ==================== FONCTIONS API BACKEND ====================

  // Fonction générique pour appeler l'API
  const callAPI = async (endpoint, options = {}) => {
    try {
      console.log(`🔗 Appel API: ${API_BASE_URL}${endpoint}`);
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      console.log(`📡 Réponse API: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ Données reçues:`, data);
      return data;
    } catch (error) {
      console.error(`❌ Erreur API ${endpoint}:`, error);
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        // Erreur de connexion au serveur
        setError('Serveur backend non accessible. Vérifiez que le serveur Node.js est démarré sur le port 3003.');
      } else {
        setError(`Erreur de connexion: ${error.message}`);
      }
      return null;
    }
  };

  // Récupérer les clients depuis la base de données
  const fetchClients = async () => {
    try {
      console.log('🔄 Récupération des clients...');
      const data = await callAPI('/api/table/client');
      if (data && data.success) {
        setClients(data.data);
        console.log(`✅ ${data.data.length} clients chargés depuis la base`);
      } else {
        console.log('⚠️ Aucun client trouvé, utilisation de données par défaut');
        setClients([]);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des clients:', error);
      setClients([]); // Données par défaut
      throw error; // Relancer pour le try-catch parent
    }
  };

  // Récupérer les consommations depuis la base de données
  const fetchConsommations = async () => {
    try {
      console.log('🔄 Récupération des consommations...');
      const data = await callAPI('/api/table/consommation');
      if (data && data.success) {
        setConsommations(data.data);
        console.log(`✅ ${data.data.length} consommations chargées depuis la base`);
      } else {
        console.log('⚠️ Aucune consommation trouvée');
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des consommations:', error);
    }
  };

  // Récupérer les contrats depuis la base de données (au lieu des factures)
  const fetchFactures = async () => {
    try {
      const data = await callAPI('/api/table/contract');
      if (data && data.success) {
        setFactures(data.data);
        console.log(`✅ ${data.data.length} contrats chargés depuis la base`);
      } else {
        // Si la table contract n'existe pas, utiliser des données vides
        console.log('⚠️ Table contract non trouvée, utilisation de données vides');
        setFactures([]);
      }
    } catch (error) {
      console.log('⚠️ Erreur lors du chargement des contrats, utilisation de données vides');
      setFactures([]);
    }
  };

  // Récupérer l'historique du technicien
  const fetchHistorique = async () => {
    // Utiliser l'ID du technicien ou un ID par défaut
    const techId = (user && user.idtech) ? user.idtech : 1;
    console.log(`🔍 Récupération historique pour technicien ID: ${techId}`);

    const data = await callAPI(`/api/technician/dashboard/${techId}`);
    if (data && data.success) {
      setHistorique(data.data.statistiques?.dernieres_consommations || []);
      setDashboardStats(data.data.statistiques || {});
      console.log(`✅ Historique technicien ${techId} chargé`);
    } else {
      console.log(`⚠️ Aucun historique trouvé pour technicien ${techId}`);
    }
  };

  // Scanner un QR code
  const scanQRCode = async (qrCode) => {
    const data = await callAPI(`/api/scan/${qrCode}`);
    if (data && data.success) {
      console.log('✅ QR Code scanné:', data.data);
      return data.data;
    }
    return null;
  };

  // Ajouter une nouvelle consommation
  const addConsommation = async (consommationData) => {
    const data = await callAPI('/api/consommations', {
      method: 'POST',
      body: JSON.stringify({
        consommationActuelle: consommationData.valeur,
        idContract: consommationData.clientId,
        idTech: user?.idtech || 1,
        periode: consommationData.date
      })
    });
    
    if (data && data.success) {
      console.log('✅ Consommation ajoutée:', data.data);
      await fetchConsommations(); // Recharger les consommations
      return true;
    }
    return false;
  };

  // ==================== CHARGEMENT DES DONNÉES AU DÉMARRAGE ====================
  
  useEffect(() => {
    const loadDashboardData = async () => {
      console.log('🔄 Chargement des données du dashboard...');
      setLoading(true);
      setError(null);

      // Timeout de sécurité : afficher le dashboard après 5 secondes maximum
      const timeoutId = setTimeout(() => {
        console.log('⏰ Timeout atteint, affichage du dashboard sans données');
        setLoading(false);
        setError(null);
      }, 5000);

      try {
        // Essayer de charger les données, mais continuer même en cas d'erreur
        console.log('📊 Étape 1: Chargement des clients...');
        try {
          await fetchClients();
        } catch (e) {
          console.warn('⚠️ Clients non chargés:', e.message);
        }

        console.log('📊 Étape 2: Chargement des consommations...');
        try {
          await fetchConsommations();
        } catch (e) {
          console.warn('⚠️ Consommations non chargées:', e.message);
        }

        console.log('📊 Étape 3: Chargement de l\'historique...');
        try {
          await fetchHistorique();
        } catch (e) {
          console.warn('⚠️ Historique non chargé:', e.message);
        }

        console.log('📊 Étape 4: Chargement des factures...');
        try {
          await fetchFactures();
        } catch (e) {
          console.warn('⚠️ Factures non chargées:', e.message);
        }

        console.log('✅ Dashboard chargé (avec ou sans données)');
        clearTimeout(timeoutId);
        setLoading(false);
      } catch (error) {
        console.error('❌ Erreur critique lors du chargement:', error);
        // Afficher le dashboard même en cas d'erreur pour permettre la navigation
        clearTimeout(timeoutId);
        setLoading(false);
        setError(null); // Ne pas bloquer l'interface
      }
    };

    loadDashboardData();
  }, []);

  // Garder quelques interventions simulées pour l'exemple
  useEffect(() => {
    setInterventions([
      {
        id: 1,
        client: 'Maintenance programmée',
        type: 'Relevé compteurs secteur Nord',
        status: 'En cours',
        date: new Date().toISOString().split('T')[0],
        priority: 'Haute',
        adresse: 'Secteur Nord - Multiple clients'
      },
      {
        id: 2,
        client: 'Intervention urgente',
        type: 'Vérification anomalie consommation',
        status: 'Planifiée',
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        priority: 'Moyenne',
        adresse: 'À définir selon QR scan'
      }
    ]);
  }, []);

  // ==================== FONCTIONS DE GESTION DES DONNÉES ====================

  // Fonction pour ajouter une consommation (utilise l'API backend)
  const handleAddConsommation = async () => {
    if (newConsommation.clientId && newConsommation.valeur && newConsommation.date) {
      setLoading(true);
      
      const success = await addConsommation(newConsommation);
      
      if (success) {
        // Ajouter à l'historique local
        const client = clients.find(c => c.idclient?.toString() === newConsommation.clientId);
        const newEntry = {
          id: Date.now(),
          action: 'Relevé consommation',
          client: client ? `${client.nom} ${client.prenom}` : 'Client inconnu',
          date: newConsommation.date,
          valeur: newConsommation.valeur,
          technicien: user?.nom || 'Technicien'
        };
        
        setHistorique(prev => [newEntry, ...prev]);
        setNewConsommation({ clientId: '', valeur: '', date: '' });
        
        console.log('✅ Consommation ajoutée avec succès');
      } else {
        setError('Erreur lors de l\'ajout de la consommation');
      }
      
      setLoading(false);
    }
  };

  // Fonction pour scanner QR Code (utilise l'API backend)
  const handleQRScan = async (qrCode) => {
    setLoading(true);
    
    const contractData = await scanQRCode(qrCode);
    
    if (contractData) {
      // Afficher les informations du client trouvé
      const clientInfo = {
        nom: `${contractData.client_nom} ${contractData.client_prenom}`,
        adresse: contractData.adresse,
        telephone: contractData.tel,
        email: contractData.email,
        compteur: contractData.codeqr,
        marque: contractData.marquecompteur,
        serie: contractData.numseriecompteur
      };
      
      console.log('✅ Client trouvé via QR:', clientInfo);
      return clientInfo;
    } else {
      setError('QR Code non trouvé dans la base de données');
      return null;
    }
    
    setLoading(false);
  };

  // ==================== FONCTIONS DE NAVIGATION ====================

  const handleNavigate = (page) => {
    // Trouver l'item de menu correspondant
    const menuItem = menuItems.find(item => item.id === page);

    if (menuItem) {
      if (menuItem.type === 'route') {
        // Navigation vers une autre page/route en utilisant window.location
        console.log(`🔗 Navigation vers: ${menuItem.route}`);
        window.location.href = menuItem.route;
      } else if (menuItem.type === 'internal') {
        // Navigation interne dans le dashboard
        setActiveTab(page);
        setShowListesClients(false);
      }
    } else {
      // Gestion spéciale pour la saisie client
      if (page === 'saisie-client') {
        handleShowSaisieClient();
      } else {
        // Fallback pour la navigation interne
        setActiveTab(page);
        setShowListesClients(false);
      }
    }
  };

  const handleBackToOverview = () => {
    setActiveTab('overview');
    setShowListesClients(false);
  };

  const handleClientSelect = (client) => {
    const clientId = client.idclient || client.id;
    setNewConsommation({...newConsommation, clientId: clientId.toString()});
    setShowListesClients(false);
    setActiveTab('consommation');
  };

  const handleBackFromClients = () => {
    setShowListesClients(false);
    setActiveTab('overview');
  };

  const handleShowResults = (data) => {
    setReleveData(data);
    setShowResultatsReleve(true);
    setActiveTab('resultats');
  };

  const handleBackFromResults = () => {
    setShowResultatsReleve(false);
    setReleveData(null);
    setActiveTab('consommation');
  };

  const handleShowSaisieClient = () => {
    setShowSaisieClient(true);
    setActiveTab('saisie-client');
  };

  const handleBackFromSaisieClient = () => {
    setShowSaisieClient(false);
    setActiveTab('overview');
  };

  // Navigation vers les pages séparées
  const renderPageContent = () => {
    if (showListesClients) {
      return (
        <ListesClients
          onBack={handleBackFromClients}
          onClientSelect={handleClientSelect}
        />
      );
    }

    if (showResultatsReleve) {
      return <ResultatsReleveePage onBack={handleBackFromResults} newReleve={releveData} />;
    }

    if (showSaisieClient) {
      return <SaisieClientPage onBack={handleBackFromSaisieClient} />;
    }

    switch (activeTab) {
      case 'overview':
        return <OverviewPage onNavigate={handleNavigate} user={user} />;

      case 'clients':
        return (
          <ListesClients
            onBack={handleBackToOverview}
            onClientSelect={handleClientSelect}
          />
        );
      
      case 'consommation':
        return <ConsommationPage user={user} onBack={handleBackToOverview} onShowResults={handleShowResults} />;

      case 'resultats':
        return <ResultatsReleveePage onBack={handleBackFromResults} newReleve={releveData} />;
      
      case 'factures':
        return <FacturesPage onBack={handleBackToOverview} />;

      case 'scanner':
        return <ScannerPage onBack={handleBackToOverview} />;

      case 'map':
        return <MapPage onBack={handleBackToOverview} />;

      case 'historique':
        return <HistoriquePage onBack={handleBackToOverview} />;

      case 'profile':
        return <ProfilePage user={user} onBack={handleBackToOverview} onLogout={onLogout} />;
      
      default:
        return <OverviewPage onNavigate={handleNavigate} user={user} />;
    }
  };

  // ==================== RENDU DU COMPOSANT ====================

  // Affichage du chargement
  if (loading && clients.length === 0) {
    return (
      <div className="tech-mobile-container">
        <div className="tech-mobile-header">
          <div className="tech-mobile-logo">
            <AquaTrackLogo size={40} showText={true} />
          </div>
        </div>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '60vh',
          flexDirection: 'column',
          gap: '20px'
        }}>
          <div style={{ fontSize: '48px' }}>⏳</div>
          <div style={{ fontSize: '18px', color: '#666' }}>
            Connexion à la base de données...
          </div>
          <div style={{ fontSize: '14px', color: '#999' }}>
            Chargement des données depuis PostgreSQL
          </div>
        </div>
      </div>
    );
  }

  // Affichage des erreurs
  if (error) {
    return (
      <div className="tech-mobile-container">
        <div className="tech-mobile-header">
          <div className="tech-mobile-logo">
            <AquaTrackLogo size={40} showText={true} />
          </div>
          <button onClick={onLogout} className="tech-mobile-logout-btn">
            Déconnexion
          </button>
        </div>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '60vh',
          flexDirection: 'column',
          gap: '20px',
          padding: '20px'
        }}>
          <div style={{ fontSize: '48px' }}>❌</div>
          <div style={{ fontSize: '18px', color: '#d32f2f', textAlign: 'center' }}>
            {error}
          </div>
          <div style={{ fontSize: '14px', color: '#666', textAlign: 'center' }}>
            Vérifiez que le serveur Node.js est démarré sur le port 3003
          </div>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={async () => {
                console.log('🔍 Test de connexion au serveur...');
                const testResult = await callAPI('/');
                if (testResult) {
                  console.log('✅ Serveur accessible, rechargement...');
                  window.location.reload();
                } else {
                  console.log('❌ Serveur toujours inaccessible');
                }
              }}
              style={{
                padding: '10px 20px',
                backgroundColor: '#00b894',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Tester connexion
            </button>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '10px 20px',
                backgroundColor: '#6c5ce7',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-container">
      {/* Header */}
      <div className="tech-mobile-header">
        <div className="tech-mobile-logo">
          <AquaTrackLogo size={40} showText={true} />
        </div>
        <div style={{ fontSize: '12px', color: '#fff', opacity: 0.8 }}>
          📊 {clients.length} clients • 💧 {consommations.length} relevés • 📄 {factures.length} factures
        </div>
        <button onClick={onLogout} className="tech-mobile-logout-btn">
          Déconnexion
        </button>
      </div>

      {/* Contenu principal */}
      {renderPageContent()}

      {/* Navigation */}
      <div className="tech-mobile-nav">
        {menuItems.map(item => (
          <button
            key={item.id}
            className={`tech-mobile-nav-item ${activeTab === item.id ? 'active' : ''} ${item.type === 'route' ? 'nav-link' : ''}`}
            onClick={() => handleNavigate(item.id)}
            title={item.type === 'route' ? `Aller vers ${item.label}` : item.label}
          >
            <span className="tech-mobile-nav-icon">{item.icon}</span>
            <span className="tech-mobile-nav-label">
              {item.label}
              {item.type === 'route' && <span className="nav-link-indicator">→</span>}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default TechnicianDashboard;
