require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const QRCode = require('qrcode');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur de consommation...');

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facutration',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Test de connexion à la base de données
pool.connect()
  .then(client => {
    console.log('✅ Connexion à la base de données réussie');
    client.release();
  })
  .catch(err => {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
  });

// Route de test simple
app.get('/', (req, res) => {
  console.log('📡 Route / appelée');
  res.json({
    message: 'Serveur consommation fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de connexion/authentification
app.post('/login', async (req, res) => {
  try {
    console.log('🔐 Tentative de connexion...');
    console.log('Données reçues:', req.body);

    const { email, motDepass } = req.body;

    // Validation des données
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        error: 'Email et mot de passe requis'
      });
    }

    // Vérification des identifiants
    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      console.log('✅ Connexion technicien réussie');
      res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 1,
          email: '<EMAIL>',
          nom: 'Technicien',
          prenom: 'Principal',
          role: 'technicien'
        },
        redirect: '/dashboard'
      });
    } else if (email === '<EMAIL>' && motDepass === 'admin123') {
      console.log('✅ Connexion admin réussie');
      res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 2,
          email: '<EMAIL>',
          nom: 'Administrateur',
          prenom: 'Système',
          role: 'admin'
        },
        redirect: '/dashboard'
      });
    } else {
      console.log('❌ Identifiants incorrects');
      res.status(401).json({
        success: false,
        error: 'Identifiants incorrects'
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur serveur lors de la connexion'
    });
  }
});

// Route pour récupérer les contrats avec informations clients
app.get('/api/contracts', async (req, res) => {
  try {
    console.log('📋 Récupération des contrats...');
    const client = await pool.connect();
    
    const query = `
      SELECT 
        co.idcontract,
        co.codeqr,
        co.datecontract,
        co.marquecompteur,
        co.numseriecompteur,
        co.posx,
        co.posy,
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        s.nom as secteur_nom
      FROM contract co
      LEFT JOIN client c ON co.idclient = c.idclient
      LEFT JOIN secteur s ON c.ids = s.ids
      ORDER BY c.nom, c.prenom
    `;
    
    const result = await client.query(query);
    client.release();
    
    console.log(`✅ ${result.rows.length} contrats récupérés`);
    
    res.json({
      success: true,
      message: 'Contrats récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur contrats:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat spécifique
app.get('/api/contracts/:idcontract/last-consommation', async (req, res) => {
  try {
    const { idcontract } = req.params;

    const query = `
      SELECT
        idcons,
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      FROM consommation
      WHERE idcont = $1
      ORDER BY idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [idcontract]);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        message: 'Dernière consommation récupérée avec succès',
        data: result.rows[0]
      });
    } else {
      res.json({
        success: true,
        message: 'Aucune consommation trouvée pour ce contrat',
        data: null
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour récupérer la dernière consommation globale (toute dernière de la base)
app.get('/api/last-consommation-global', async (req, res) => {
  try {
    const query = `
      SELECT
        idcons,
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      FROM consommation
      ORDER BY idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(query);

    if (result.rows.length > 0) {
      res.json({
        success: true,
        message: 'Dernière consommation globale récupérée avec succès',
        data: result.rows[0]
      });
    } else {
      res.json({
        success: true,
        message: 'Aucune consommation trouvée dans la base de données',
        data: null
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation globale:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Route pour récupérer les consommations
app.get('/api/consommations', async (req, res) => {
  try {
    console.log('📋 Récupération des consommations...');
    const client = await pool.connect();
    
    const query = `
      SELECT 
        cons.idcons,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.jours,
        cons.periode,
        cons.status,
        co.codeqr,
        co.marquecompteur,
        c.nom,
        c.prenom,
        c.adresse,
        u.nom as tech_nom,
        u.prenom as tech_prenom
      FROM consommation cons
      LEFT JOIN contract co ON cons.idcont = co.idcontract
      LEFT JOIN client c ON co.idclient = c.idclient
      LEFT JOIN utilisateur u ON cons.idtech = u.idtech
      ORDER BY cons.idcons DESC
    `;
    
    const result = await client.query(query);
    client.release();
    
    console.log(`✅ ${result.rows.length} consommations récupérées`);
    
    res.json({
      success: true,
      message: 'Consommations récupérées avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur consommations:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Routes pour le dashboard
app.get('/api/table/client', async (req, res) => {
  try {
    console.log('📋 Récupération des clients pour le dashboard...');
    const client = await pool.connect();

    const query = `
      SELECT idclient, nom, prenom, ville, tel
      FROM client
      ORDER BY nom, prenom
    `;

    const result = await client.query(query);
    client.release();

    res.json({
      success: true,
      message: 'Clients récupérés',
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des clients:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/table/consommation', async (req, res) => {
  try {
    console.log('📋 Récupération des consommations pour le dashboard...');
    const client = await pool.connect();

    const query = `
      SELECT idcons, consommationpre, consommationactuelle, idcont, periode
      FROM consommation
      ORDER BY periode DESC
    `;

    const result = await client.query(query);
    client.release();

    res.json({
      success: true,
      message: 'Consommations récupérées',
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des consommations:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/table/contract', async (req, res) => {
  try {
    console.log('📋 Récupération des contrats pour le dashboard...');
    const client = await pool.connect();

    const query = `
      SELECT idcontract, codeqr, datecontract, marquecompteur
      FROM contract
      ORDER BY datecontract DESC
    `;

    const result = await client.query(query);
    client.release();

    res.json({
      success: true,
      message: 'Contrats récupérés',
      data: result.rows
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des contrats:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

app.get('/api/technician/dashboard/:id', async (req, res) => {
  const techId = req.params.id;
  console.log(`📋 Dashboard technicien ${techId}...`);

  res.json({
    success: true,
    message: 'Dashboard technicien récupéré',
    data: {
      statistiques: {
        total_clients: 15,
        consommations_ce_mois: 8,
        factures_generees: 12,
        dernieres_consommations: [
          { idcons: 1, client: "Dupont Jean", consommation: 120, date: "2025-06-25" },
          { idcons: 2, client: "Martin Marie", consommation: 135, date: "2025-06-24" }
        ]
      }
    }
  });
});

app.get('/api/scan/:qrcode', async (req, res) => {
  const qrCode = req.params.qrcode;
  console.log(`📱 Scan QR Code: ${qrCode}`);

  res.json({
    success: true,
    message: 'QR Code scanné avec succès',
    data: {
      qrcode: qrCode,
      client: {
        idclient: 1,
        nom: "Dupont",
        prenom: "Jean",
        adresse: "123 Rue de la Paix, Paris"
      },
      contrat: {
        idcontract: 1,
        codeqr: qrCode,
        marquecompteur: "Sagemcom"
      }
    }
  });
});

app.get('/api/clients', async (req, res) => {
  try {
    console.log('📋 Récupération des clients...');
    const client = await pool.connect();

    const query = `
      SELECT
        idclient,
        nom,
        prenom,
        adresse,
        ville,
        tel,
        email
      FROM client
      ORDER BY nom, prenom
    `;

    const result = await client.query(query);
    client.release();

    res.json({
      success: true,
      message: 'Clients récupérés depuis la base Facutration',
      data: result.rows,
      source: 'PostgreSQL'
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des clients:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour ajouter une nouvelle consommation
app.post('/api/consommations', async (req, res) => {
  try {
    console.log('📝 Ajout d\'une nouvelle consommation...');
    console.log('Données reçues:', req.body);
    
    const { idcont, consommationpre, consommationactuelle, jours, periode, idtech } = req.body;
    
    // Validation des données
    if (!idcont || !consommationactuelle) {
      return res.status(400).json({
        success: false,
        error: 'Les champs idcont et consommationactuelle sont obligatoires'
      });
    }
    
    console.log('🔗 Connexion à la base de données...');
    const client = await pool.connect();

    const query = `
      INSERT INTO consommation (
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      consommationpre || 0,
      consommationactuelle,
      idcont,
      idtech || 1, // ID technicien par défaut
      1, // ID tranche par défaut
      jours || 30,
      periode || new Date().toISOString().slice(0, 7),
      'nouveau'
    ];

    console.log('📝 Exécution de la requête SQL...');
    console.log('Query:', query);
    console.log('Values:', values);

    const result = await client.query(query, values);
    client.release();

    console.log('✅ Requête exécutée avec succès');
    
    console.log('✅ Consommation ajoutée:', result.rows[0]);
    
    res.json({
      success: true,
      message: 'Consommation ajoutée avec succès',
      data: result.rows[0]
    });
  } catch (err) {
    console.error('❌ Erreur ajout consommation:', err);
    res.status(500).json({
      success: false,
      error: err.message
    });
  }
});

// Route pour récupérer les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📋 Récupération des secteurs depuis la table secteur...');
    const client = await pool.connect();

    // Récupérer les secteurs depuis votre table secteur (ids, nom)
    const query = `SELECT ids, nom FROM secteur ORDER BY nom`;
    const result = await client.query(query);

    client.release();

    console.log(`✅ ${result.rows.length} secteurs récupérés depuis la base de données`);
    console.log('Secteurs:', result.rows);

    res.json({
      success: true,
      message: 'Secteurs récupérés avec succès',
      count: result.rows.length,
      data: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    console.error('Détails de l\'erreur:', error.message);

    // En cas d'erreur, retourner des secteurs par défaut
    res.json({
      success: true,
      message: 'Secteurs par défaut utilisés (erreur de connexion)',
      count: 3,
      data: [
        { ids: 1, nom: 'Résidentiel' },
        { ids: 2, nom: 'Commercial' },
        { ids: 3, nom: 'Industriel' }
      ]
    });
  }
});

// Route pour ajouter un nouveau client
app.post('/api/clients', async (req, res) => {
  try {
    console.log('➕ Ajout d\'un nouveau client...');
    console.log('Données reçues:', req.body);

    const { nom, prenom, adresse, ville, tel, email, ids } = req.body;

    // Validation des champs obligatoires
    if (!nom || !prenom || !ville) {
      return res.status(400).json({
        success: false,
        error: 'Les champs nom, prénom et ville sont obligatoires'
      });
    }

    const client = await pool.connect();

    // Générer un code QR unique pour ce client
    const clientCode = `CLIENT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log('🔗 Code QR généré:', clientCode);

    // Générer l'image QR code en base64
    const qrCodeImage = await QRCode.toDataURL(clientCode, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // Essayer d'abord avec la colonne codeqr
    let query = `
      INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids, codeqr)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;
    let values = [nom, prenom, adresse, ville, tel, email, ids || null, clientCode];

    try {
      const result = await client.query(query, values);
      client.release();

      console.log(`✅ Client ajouté avec succès avec code QR - ID: ${result.rows[0].idclient}`);
      res.json({
        success: true,
        message: 'Client ajouté avec succès',
        data: {
          ...result.rows[0],
          qrCodeImage: qrCodeImage // Inclure l'image QR code en base64
        }
      });
    } catch (dbError) {
      // Si la colonne codeqr n'existe pas, essayer sans
      if (dbError.message.includes('column "codeqr"')) {
        console.log('⚠️ Colonne codeqr n\'existe pas, ajout sans code QR...');
        query = `
          INSERT INTO client (nom, prenom, adresse, ville, tel, email, ids)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
          RETURNING *
        `;
        values = [nom, prenom, adresse, ville, tel, email, ids || null];
        const result = await client.query(query, values);
        client.release();

        console.log(`✅ Client ajouté avec succès sans code QR - ID: ${result.rows[0].idclient}`);
        res.json({
          success: true,
          message: 'Client ajouté avec succès',
          data: {
            ...result.rows[0],
            codeqr: clientCode, // Ajouter le code généré même si pas sauvé en DB
            qrCodeImage: qrCodeImage
          },
          warning: 'Code QR généré mais non sauvé en base (colonne manquante)'
        });
      } else {
        client.release();
        throw dbError;
      }
    }
  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout du client:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de l\'ajout du client'
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur consommation démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/contracts (liste contrats avec clients)');
  console.log('  - GET /api/consommations (liste consommations)');
  console.log('  - POST /api/consommations (ajouter consommation)');
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});
