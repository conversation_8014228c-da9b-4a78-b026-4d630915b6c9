console.log('🚀 Démarrage du serveur...');

const express = require('express');
const cors = require('cors');

console.log('📦 Modules chargés');

const app = express();
const PORT = 9999;

console.log('⚙️ Configuration du serveur...');

app.use(cors());
app.use(express.json());

console.log('🔧 Middleware configuré');

app.get('/', (req, res) => {
  console.log('📥 GET / reçu');
  res.json({ message: 'Serveur fonctionnel', timestamp: new Date().toISOString() });
});

app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 GET /api/last-consommation-global reçu');
  res.json({
    success: true,
    message: 'Dernière consommation récupérée',
    data: { consommationactuelle: 135 }
  });
});

app.get('/api/contracts', (req, res) => {
  console.log('📥 GET /api/contracts reçu');
  res.json({
    success: true,
    message: 'Contrats récupérés',
    data: [{ idcontract: 10, nom: "Dupont", prenom: "Jean" }]
  });
});

app.post('/api/consommations', (req, res) => {
  console.log('📝 POST /api/consommations reçu');
  console.log('📋 Données reçues:', JSON.stringify(req.body, null, 2));
  
  const response = {
    success: true,
    message: 'Consommation enregistrée avec succès',
    data: {
      id: Math.floor(Math.random() * 1000) + 100,
      ...req.body,
      timestamp: new Date().toISOString()
    }
  };
  
  console.log('✅ Réponse envoyée:', JSON.stringify(response, null, 2));
  res.json(response);
});

console.log('🌐 Routes configurées');

const server = app.listen(PORT, () => {
  console.log(`✅ Serveur démarré avec succès sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
  console.log('🎯 Le serveur est prêt à recevoir des requêtes');
});

server.on('error', (error) => {
  console.error('❌ Erreur du serveur:', error);
});

console.log('🔄 Script terminé, serveur en écoute...');
