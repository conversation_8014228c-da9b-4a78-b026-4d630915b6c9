const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3005;

// Middleware
app.use(cors());
app.use(express.json());

// Route de test simple
app.get('/', (req, res) => {
  console.log('✅ Requête reçue sur /');
  res.json({
    message: 'Serveur de test fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de test pour les clients
app.get('/api/table/client', (req, res) => {
  console.log('✅ Requête reçue sur /api/table/client');
  res.json({
    success: true,
    data: [
      { idclient: 1, nom: 'Test', prenom: 'Client', adresse: 'Test Address' }
    ]
  });
});

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET  / (test)');
  console.log('  - GET  /api/table/client (test clients)');
});

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});
