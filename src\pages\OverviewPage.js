import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const OverviewPage = ({ user, onNavigate }) => {
  const [stats, setStats] = useState({
    interventions: 0,
    interventionsEnCours: 0,
    clients: 0,
    relevesAujourdhui: 0
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Charger les statistiques dynamiques depuis la base de données
      const API_BASE_URL = 'http://localhost:3000';
      const response = await fetch(`${API_BASE_URL}/api/dashboard/stats`);
      const data = await response.json();

      if (data.success) {
        console.log('✅ Statistiques chargées depuis la base:', data.data);
        setStats(data.data);
      } else {
        console.error('❌ Erreur lors du chargement des statistiques:', data.message);
        // Fallback vers des données par défaut en cas d'erreur
        setStats({
          interventions: 0,
          interventionsEnCours: 0,
          clients: 0,
          relevesAujourdhui: 0
        });
      }

    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      // En cas d'erreur de connexion, utiliser des valeurs par défaut
      setStats({
        interventions: 0,
        interventionsEnCours: 0,
        clients: 0,
        relevesAujourdhui: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      id: 'scan',
      title: 'Scanner QR',
      description: 'Identifier un compteur',
      icon: '📱',
      color: '#6366f1'
    },
    {
      id: 'consommation',
      title: 'Nouveau Relevé',
      description: 'Saisir consommation',
      icon: '💧',
      color: '#059669'
    },
    {
      id: 'clients',
      title: 'CLIENTS →',
      description: 'Liste des clients',
      icon: '👥',
      color: '#6c5ce7'
    },
    {
      id: 'saisie-client',
      title: 'Nouveau Client',
      description: 'Ajouter un client',
      icon: '👤',
      color: '#f59e0b'
    },
    {
      id: 'map',
      title: 'Localisation',
      description: 'Carte des clients',
      icon: '🗺️',
      color: '#7c3aed'
    }
  ];

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement du tableau de bord...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      {/* En-tête de bienvenue */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <div>
            <h1 className="tech-mobile-card-title">
              Bonjour {user?.prenom || 'Technicien'} ! 👋
            </h1>
            <p className="tech-mobile-card-subtitle">
              {new Date().toLocaleDateString('fr-FR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="tech-mobile-stats-grid">
        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">🔧</div>
          <div className="tech-mobile-stat-number">{stats.interventions}</div>
          <div className="tech-mobile-stat-label">Interventions</div>
        </div>

        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">⏳</div>
          <div className="tech-mobile-stat-number">{stats.interventionsEnCours}</div>
          <div className="tech-mobile-stat-label">En cours</div>
        </div>

        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">👥</div>
          <div className="tech-mobile-stat-number">{stats.clients}</div>
          <div className="tech-mobile-stat-label">Clients</div>
        </div>

        <div className="tech-mobile-stat-card">
          <div className="tech-mobile-stat-icon">💧</div>
          <div className="tech-mobile-stat-number">{stats.relevesAujourdhui}</div>
          <div className="tech-mobile-stat-label">Relevés aujourd'hui</div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">⚡ Actions Rapides</h2>
        </div>
        <div className="tech-mobile-quick-actions">
          {quickActions.map(action => (
            <button
              key={action.id}
              className="tech-mobile-quick-action"
              onClick={() => onNavigate(action.id)}
              style={{ borderColor: action.color }}
            >
              <div 
                className="tech-mobile-quick-action-icon"
                style={{ backgroundColor: action.color }}
              >
                {action.icon}
              </div>
              <div className="tech-mobile-quick-action-content">
                <div className="tech-mobile-quick-action-title">{action.title}</div>
                <div className="tech-mobile-quick-action-desc">{action.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>


    </div>
  );
};

export default OverviewPage;
