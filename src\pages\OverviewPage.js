import React from 'react';
import '../TechnicianDashboard.css';

const OverviewPage = ({ user, onNavigate }) => {
  const quickActions = [
    {
      id: 'scan',
      title: 'Scanner QR',
      description: 'Identifier un compteur',
      icon: '📱',
      color: '#6366f1'
    },
    {
      id: 'consommation',
      title: 'Nouveau Relevé',
      description: 'Saisir consommation',
      icon: '💧',
      color: '#059669'
    },
    {
      id: 'clients',
      title: 'CLIENTS →',
      description: 'Liste des clients',
      icon: '👥',
      color: '#6c5ce7'
    },
    {
      id: 'saisie-client',
      title: 'Nouveau Client',
      description: 'Ajouter un client',
      icon: '👤',
      color: '#f59e0b'
    },
    {
      id: 'map',
      title: 'Localisation',
      description: 'Carte des clients',
      icon: '🗺️',
      color: '#7c3aed'
    }
  ];

  return (
    <div className="tech-mobile-content">
      {/* En-tête de bienvenue */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <div>
            <h1 className="tech-mobile-card-title">
              Bonjour {user?.prenom || 'Technicien'} ! 👋
            </h1>
            <p className="tech-mobile-card-subtitle">
              {new Date().toLocaleDateString('fr-FR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Message mode offline */}
      <div className="tech-mobile-card" style={{
        backgroundColor: '#e3f2fd',
        border: '1px solid #2196f3',
        marginBottom: '15px'
      }}>
        <div className="tech-mobile-card-header">
          <div style={{
            textAlign: 'center',
            color: '#1976d2',
            fontSize: '14px'
          }}>
            📡 <strong>Mode Offline</strong> - Utilisation des données de la base Facutration (simulation)
            <br />
            <small>Toutes les fonctionnalités sont disponibles avec des données de test</small>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <h2 className="tech-mobile-card-title">⚡ Actions Rapides</h2>
        </div>
        <div className="tech-mobile-quick-actions">
          {quickActions.map(action => (
            <button
              key={action.id}
              className="tech-mobile-quick-action"
              onClick={() => onNavigate(action.id)}
              style={{ borderColor: action.color }}
            >
              <div 
                className="tech-mobile-quick-action-icon"
                style={{ backgroundColor: action.color }}
              >
                {action.icon}
              </div>
              <div className="tech-mobile-quick-action-content">
                <div className="tech-mobile-quick-action-title">{action.title}</div>
                <div className="tech-mobile-quick-action-desc">{action.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default OverviewPage;
