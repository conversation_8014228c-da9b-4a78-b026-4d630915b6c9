const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3006;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur de correction...');

// Route de test
app.get('/', (req, res) => {
  console.log('📥 Requête GET /');
  res.json({ 
    message: 'Serveur de correction fonctionnel', 
    timestamp: new Date().toISOString() 
  });
});

// Route pour récupérer la dernière consommation globale
app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 Récupération de la dernière consommation globale...');
  
  res.json({
    success: true,
    message: 'Dernière consommation globale récupérée avec succès',
    data: {
      idcons: 14,
      consommationpre: 150,
      consommationactuelle: 122,
      idcont: 10,
      idtech: 1,
      idtranch: 1,
      jours: 30,
      periode: "juin 2025",
      status: "actif"
    }
  });
});

// Route pour récupérer les contrats
app.get('/api/contracts', (req, res) => {
  console.log('📥 Récupération des contrats...');
  
  res.json({
    success: true,
    message: 'Contrats récupérés avec succès',
    count: 1,
    data: [{
      idcontract: 10,
      codeqr: "QR123",
      datecontract: "2024-01-15T09:00:00.000Z",
      marquecompteur: "Sagemcom",
      nom: "Dupont",
      prenom: "Jean",
      ville: "Paris"
    }]
  });
});

// Route POST pour enregistrer une consommation
app.post('/api/consommations', (req, res) => {
  console.log('📝 Enregistrement d\'une nouvelle consommation...');
  console.log('Données reçues:', req.body);
  
  try {
    // Simulation d'un succès
    const response = {
      success: true,
      message: 'Consommation enregistrée avec succès',
      data: {
        idcons: Math.floor(Math.random() * 1000) + 100,
        ...req.body,
        timestamp: new Date().toISOString()
      }
    };
    
    console.log('✅ Réponse envoyée:', response);
    res.json(response);
  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement',
      error: error.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur de correction démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
