// server/facture.js
const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la connexion PostgreSQL pour la base "Facutration"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration', // Votre base de données
  password: '123456', // Votre mot de passe
  port: 5432,
});

const PORT = 3004; // Port différent pour éviter les conflits

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur Factures démarré',
    database: 'Facutration',
    routes: [
      'GET /api/factures - Toutes les factures',
      'GET /api/factures/:id - Facture par ID',
      'GET /api/factures/client/:idClient - Factures d\'un client',
      'GET /api/factures/status/:status - Factures par statut'
    ]
  });
});

/**
 * GET /api/factures - Récupère toutes les factures avec informations client
 */
app.get('/api/factures', async (req, res) => {
  try {
    console.log('📋 Récupération de toutes les factures...');

    const query = `
      SELECT
        f.idfact,
        f.date,
        f.idconst,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        c.idcont,
        cl.nom,
        cl.prenom,
        cl.ville,
        cl.adresse,
        cl.tel,
        cl.email
      FROM facture f
      JOIN consommation c ON f.idconst = c.idcons
      JOIN contract ct ON c.idcont = ct.idcontract
      JOIN client cl ON ct.idclient = cl.idclient
      ORDER BY f.date DESC
    `;

    const { rows } = await pool.query(query);

    console.log(`✅ ${rows.length} factures récupérées`);

    res.json({
      success: true,
      message: 'Factures récupérées avec succès',
      count: rows.length,
      data: rows
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des factures:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération des factures',
      details: err.message
    });
  }
});

/**
 * GET /api/factures/:id - Récupère une facture spécifique
 */
app.get('/api/factures/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🔍 Récupération de la facture ID: ${id}`);
    const query = `
      SELECT
        f.idfact,
        f.date,
        f.idconst,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        c.idcont,
        cl.nom,
        cl.prenom,
        cl.ville,
        cl.adresse,
        cl.tel,
        cl.email
      FROM facture f
      JOIN consommation c ON f.idconst = c.idcons
      JOIN contract ct ON c.idcont = ct.idcontract
      JOIN client cl ON ct.idclient = cl.idclient
      WHERE f.idfact = $1
    `;

    const { rows } = await pool.query(query, [id]);

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Facture non trouvée'
      });
    }

    console.log(`✅ Facture ${id} récupérée`);
    res.json({
      success: true,
      message: 'Facture récupérée avec succès',
      data: rows[0]
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération de la facture:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération de la facture',
      details: err.message
    });
  }
});

/**
 * GET /api/factures/client/:idClient - Récupère les factures d'un client
 */
app.get('/api/factures/client/:idClient', async (req, res) => {
  try {
    const { idClient } = req.params;
    console.log(`🔍 Récupération des factures pour le client ID: ${idClient}`);

    const query = `
      SELECT
        f.idfact,
        f.date,
        f.idconst,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        c.idcont,
        cl.nom,
        cl.prenom,
        cl.ville
      FROM facture f
      JOIN consommation c ON f.idconst = c.idcons
      JOIN contract ct ON c.idcont = ct.idcontract
      JOIN client cl ON ct.idclient = cl.idclient
      WHERE ct.idclient = $1
      ORDER BY f.date DESC
    `;

    const { rows } = await pool.query(query, [idClient]);

    console.log(`✅ ${rows.length} factures trouvées pour le client ${idClient}`);
    res.json({
      success: true,
      message: `Factures du client ${idClient} récupérées avec succès`,
      count: rows.length,
      data: rows
    });
  } catch (err) {
    console.error('❌ Erreur lors de la récupération des factures du client:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération des factures du client',
      details: err.message
    });
  }
});

/**
 * GET /api/factures/status/:status - Filtre les factures par statut
 */
app.get('/api/factures/status/:status', async (req, res) => {
  try {
    const { status } = req.params;
    const validStatuses = ['payée', 'nonpayée'];

    console.log(`🔍 Filtrage des factures par statut: ${status}`);

    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Statut invalide. Utilisez "payée" ou "nonpayée"'
      });
    }

    const query = `
      SELECT
        f.idfact,
        f.date,
        f.idconst,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        c.idcont,
        cl.nom,
        cl.prenom,
        cl.ville
      FROM facture f
      JOIN consommation c ON f.idconst = c.idcons
      JOIN contract ct ON c.idcont = ct.idcontract
      JOIN client cl ON ct.idclient = cl.idclient
      WHERE f.status = $1
      ORDER BY f.date DESC
    `;

    const { rows } = await pool.query(query, [status]);

    console.log(`✅ ${rows.length} factures trouvées avec le statut "${status}"`);
    res.json({
      success: true,
      message: `Factures avec statut "${status}" récupérées avec succès`,
      count: rows.length,
      data: rows
    });
  } catch (err) {
    console.error('❌ Erreur lors du filtrage des factures:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors du filtrage des factures',
      details: err.message
    });
  }
});

/**
 * POST /api/factures - Créer une nouvelle facture basée sur une consommation
 */
app.post('/api/factures', async (req, res) => {
  try {
    const { idconst, reference } = req.body;
    console.log(`💰 Création d'une nouvelle facture pour consommation ID: ${idconst}`);

    if (!idconst) {
      return res.status(400).json({
        success: false,
        error: 'ID de consommation requis'
      });
    }

    // Récupérer les données de consommation
    const consommationQuery = `
      SELECT
        c.*,
        ct.idclient,
        tr.prix as tarif_prix
      FROM consommation c
      JOIN contract ct ON c.idcont = ct.idcontract
      LEFT JOIN tranch tr ON c.idtranch = tr.idtranch
      WHERE c.idcons = $1
    `;

    const { rows: consommationRows } = await pool.query(consommationQuery, [idconst]);

    if (consommationRows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Consommation non trouvée'
      });
    }

    const consommation = consommationRows[0];

    // Calculer le montant de la facture
    const consommationM3 = consommation.consommationactuelle - consommation.consommationpre;
    const tarifParM3 = consommation.tarif_prix || 7.00;
    const forfaitMensuel = 10.00;
    const montantTotal = (consommationM3 * tarifParM3) + forfaitMensuel;

    // Créer la facture
    const insertQuery = `
      INSERT INTO facture (date, idconst, montant, periode, reference, status)
      VALUES (NOW(), $1, $2, $3, $4, 'nonpayée')
      RETURNING *
    `;

    const { rows: factureRows } = await pool.query(insertQuery, [
      idconst,
      montantTotal,
      consommation.periode,
      reference || Date.now()
    ]);

    const nouvelleFacture = factureRows[0];

    console.log(`✅ Facture créée avec ID: ${nouvelleFacture.idfact}`);

    res.json({
      success: true,
      message: 'Facture créée avec succès',
      data: {
        ...nouvelleFacture,
        consommationM3: consommationM3,
        tarifParM3: tarifParM3,
        forfaitMensuel: forfaitMensuel
      }
    });

  } catch (err) {
    console.error('❌ Erreur lors de la création de la facture:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la création de la facture',
      details: err.message
    });
  }
});

/**
 * PUT /api/factures/:id/status - Mettre à jour le statut d'une facture
 */
app.put('/api/factures/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    console.log(`🔄 Mise à jour du statut de la facture ${id} vers: ${status}`);

    if (!['payée', 'nonpayée'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Statut invalide. Utilisez "payée" ou "nonpayée"'
      });
    }

    const updateQuery = `
      UPDATE facture
      SET status = $1
      WHERE idfact = $2
      RETURNING *
    `;

    const { rows } = await pool.query(updateQuery, [status, id]);

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Facture non trouvée'
      });
    }

    console.log(`✅ Statut de la facture ${id} mis à jour`);

    res.json({
      success: true,
      message: 'Statut de la facture mis à jour avec succès',
      data: rows[0]
    });

  } catch (err) {
    console.error('❌ Erreur lors de la mise à jour du statut:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la mise à jour du statut',
      details: err.message
    });
  }
});

/**
 * GET /api/factures/:id/pdf - Génère une facture PDF
 */
app.get('/api/factures/:id/pdf', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📄 Génération PDF pour la facture ID: ${id}`);

    // Récupérer les données complètes de la facture
    const query = `
      SELECT
        f.idfact,
        f.date,
        f.idconst,
        f.montant,
        f.periode,
        f.reference,
        f.status,
        c.consommationpre,
        c.consommationactuelle,
        c.jours,
        c.idcont,
        ct.codeqr,
        ct.marquecompteur,
        cl.nom,
        cl.prenom,
        cl.adresse,
        cl.ville,
        cl.tel as telephone,
        tr.prix as tarif_prix
      FROM facture f
      JOIN consommation c ON f.idconst = c.idcons
      JOIN contract ct ON c.idcont = ct.idcontract
      JOIN client cl ON ct.idclient = cl.idclient
      LEFT JOIN tranch tr ON c.idtranch = tr.idtranch
      WHERE f.idfact = $1
    `;

    const { rows } = await pool.query(query, [id]);

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Facture non trouvée'
      });
    }

    const factureData = rows[0];

    // Créer le document PDF
    const doc = new PDFDocument({ margin: 50 });

    // Configuration de la réponse HTTP
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename=facture-${factureData.idfact}.pdf`);

    // Pipe le PDF vers la réponse
    doc.pipe(res);

    // Générer le contenu du PDF
    generateFacturePDF(doc, factureData);

    // Finaliser le document
    doc.end();

    console.log(`✅ PDF généré pour la facture ${id}`);

  } catch (err) {
    console.error('❌ Erreur lors de la génération PDF:', err);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la génération du PDF',
      details: err.message
    });
  }
});

// Fonction pour générer le contenu du PDF
function generateFacturePDF(doc, data) {
  // En-tête avec logo et informations de l'entreprise AquaTrack

  // Logo AquaTrack sophistiqué
  // Cercle principal bleu
  doc.circle(80, 70, 30)
     .fillAndStroke('#0066CC', '#004499');

  // Goutte d'eau principale (forme de goutte)
  doc.fillColor('#FFFFFF')
     .moveTo(80, 45)
     .bezierCurveTo(70, 55, 70, 65, 80, 75)
     .bezierCurveTo(90, 65, 90, 55, 80, 45)
     .fill();

  // Petites gouttes décoratives
  doc.fillColor('#87CEEB')
     .circle(65, 55, 3).fill()
     .circle(95, 60, 2).fill()
     .circle(75, 85, 2).fill();

  // Vagues en bas du logo
  doc.strokeColor('#FFFFFF')
     .lineWidth(2)
     .moveTo(55, 85)
     .bezierCurveTo(65, 82, 75, 88, 85, 85)
     .bezierCurveTo(95, 82, 105, 88, 105, 85)
     .stroke();

  // Nom de l'entreprise
  doc.fillColor('#0066CC')
     .fontSize(20)
     .font('Helvetica-Bold')
     .text('AquaTrack', 130, 55)
     .fillColor('#004499')
     .fontSize(14)
     .text('أكوا تراك', 420, 55)
     .fillColor('#666666')
     .fontSize(12)
     .font('Helvetica')
     .text('Système de Gestion d\'Eau Intelligent', 130, 80)
     .text('نظام إدارة المياه الذكي', 420, 80);

  // Titre principal avec style AquaTrack
  doc.fillColor('#0066CC')
     .fontSize(16)
     .font('Helvetica-Bold')
     .text('FACTURE DE CONSOMMATION D\'EAU', 50, 130, { align: 'center' })
     .fillColor('#004499')
     .fontSize(14)
     .text('فاتورة استهلاك المياه - AquaTrack', 50, 150, { align: 'center' });

  // Informations client DYNAMIQUES
  const yStart = 190;

  console.log('📄 Données client pour PDF:', {
    nom: data.nom,
    prenom: data.prenom,
    adresse: data.adresse,
    ville: data.ville,
    telephone: data.telephone
  });

  doc.fontSize(10)
     .font('Helvetica')
     .text('Nom/Prénom:', 50, yStart)
     .text(`${data.nom || 'N/A'} ${data.prenom || 'N/A'}`, 120, yStart)
     .text('Adresse:', 50, yStart + 15)
     .text(data.adresse || 'Adresse non renseignée', 120, yStart + 15)
     .text('Ville:', 50, yStart + 30)
     .text(data.ville || 'N/A', 120, yStart + 30)
     .text('Téléphone:', 50, yStart + 45)
     .text(data.telephone || 'N/A', 120, yStart + 45)
     .text('Code QR:', 50, yStart + 60)
     .text(data.codeqr || 'N/A', 120, yStart + 60);

  // Informations de facturation DYNAMIQUES
  doc.text('Référence:', 300, yStart)
     .text(data.reference || data.idfact, 370, yStart)
     .text('Date:', 300, yStart + 15)
     .text(new Date(data.date).toLocaleDateString('fr-FR'), 370, yStart + 15)
     .text('Période:', 300, yStart + 30)
     .text(data.periode, 370, yStart + 30)
     .text('Contrat:', 300, yStart + 45)
     .text(data.idcont || 'N/A', 370, yStart + 45);

  // Ligne de séparation élégante
  doc.strokeColor('#0066CC')
     .lineWidth(2)
     .moveTo(50, 290)
     .lineTo(550, 290)
     .stroke();

  // Tableau de consommation
  const tableTop = 310;

  // En-têtes du tableau avec style AquaTrack
  doc.rect(50, tableTop, 500, 25)
     .fillAndStroke('#0066CC', '#004499');

  doc.fillColor('#FFFFFF')
     .fontSize(10)
     .font('Helvetica-Bold')
     .text('Tranches', 60, tableTop + 8)
     .text('Relevé en m³', 150, tableTop + 8)
     .text('Tarif en DH/m³', 250, tableTop + 8)
     .text('Valeur à payer (DH)', 400, tableTop + 8);

  // Calculs DYNAMIQUES
  const consommationPre = parseInt(data.consommationpre) || 0;
  const consommationActuelle = parseInt(data.consommationactuelle) || 0;
  const consommation = consommationActuelle - consommationPre;
  const tarifPrix = parseFloat(data.tarif_prix) || 7.00;

  console.log(`📊 Calculs PDF: ${consommationPre} → ${consommationActuelle} = ${consommation} m³ × ${tarifPrix} DH/m³`);

  // Lignes du tableau
  let currentY = tableTop + 25;

  // Tranche 1 (0-10 m³)
  const tranche1 = Math.min(consommation, 10);
  const montant1 = tranche1 * tarifPrix;

  doc.rect(50, currentY, 500, 22)
     .fillAndStroke('#F0F8FF', '#0066CC');
  doc.fillColor('#000000')
     .font('Helvetica')
     .text('1', 60, currentY + 6)
     .text(`1 - ${tranche1}`, 150, currentY + 6)
     .text(tarifPrix.toFixed(2), 250, currentY + 6)
     .text(montant1.toFixed(2), 400, currentY + 6);

  currentY += 22;

  // Tranche 2 (si consommation > 10 m³)
  let montant2 = 0;
  if (consommation > 10) {
    const tranche2 = consommation - 10;
    montant2 = tranche2 * tarifPrix;

    doc.rect(50, currentY, 500, 22)
       .fillAndStroke('#E6F3FF', '#0066CC');
    doc.fillColor('#000000')
       .text('2', 60, currentY + 6)
       .text(`${tranche1 + 1} - ${consommation}`, 150, currentY + 6)
       .text(tarifPrix.toFixed(2), 250, currentY + 6)
       .text(montant2.toFixed(2), 400, currentY + 6);

    currentY += 22;
  }

  currentY += 10;

  // Total avec style
  doc.rect(50, currentY, 500, 25)
     .fillAndStroke('#0066CC', '#004499');
  doc.fillColor('#FFFFFF')
     .font('Helvetica-Bold')
     .text('TOTAL m³', 150, currentY + 8)
     .text(consommation.toString(), 250, currentY + 8);

  currentY += 40;

  // Calculs des montants DYNAMIQUES
  const totalConsommation = montant1 + montant2;
  const forfaitMensuel = 10.00;
  const montantTotal = totalConsommation + forfaitMensuel;

  console.log(`💰 Montants: Consommation=${totalConsommation.toFixed(2)} + Forfait=${forfaitMensuel} = Total=${montantTotal.toFixed(2)} DH`);

  // Montants
  doc.font('Helvetica')
     .text('Ancien Index:', 60, currentY)
     .text(consommationPre.toString(), 150, currentY)
     .text('Forfait mensuel 10 dh/mois:', 300, currentY)
     .text(forfaitMensuel.toFixed(2), 450, currentY);

  currentY += 20;
  doc.text('Nouveau:', 60, currentY)
     .text(consommationActuelle.toString(), 150, currentY)
     .text('Sous-total consommation:', 300, currentY)
     .text(totalConsommation.toFixed(2), 450, currentY);

  currentY += 20;
  doc.text('Consommation en m3:', 60, currentY)
     .text(consommation.toString(), 150, currentY)
     .text('TOTAL À PAYER:', 300, currentY)
     .font('Helvetica-Bold')
     .text(`${montantTotal.toFixed(2)} DH`, 450, currentY);

  currentY += 20;
  doc.font('Helvetica')
     .text('Nombre de jours:', 60, currentY)
     .text((data.jours || 'N/A').toString(), 150, currentY);

  // Statut de paiement DYNAMIQUE
  currentY += 30;
  const statusColor = data.status === 'payée' ? 'green' : 'red';
  const statusText = data.status === 'payée' ? 'PAYÉE' : 'NON PAYÉE';
  doc.fontSize(14)
     .font('Helvetica-Bold')
     .fillColor(statusColor)
     .text(`Statut: ${statusText}`, 250, currentY);

  // Pied de page
  doc.fontSize(8)
     .fillColor('black')
     .font('Helvetica')
     .text('Cette facture est générée automatiquement par le système de facturation', 50, 650)
     .text(`Facture ID: ${data.idfact} - Consommation ID: ${data.idconst}`, 50, 665)
     .text('ملاحظة: يجب دفع هذه الفاتورة في أجل أقصاه الأول من الشهر الموالي', 50, 680, { align: 'center' });
}

// Test de connexion à la base de données
pool.connect()
  .then(() => {
    console.log('✅ Connexion à la base de données Facutration réussie');
  })
  .catch(err => {
    console.error('❌ Erreur de connexion à la base de données:', err);
  });

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur Factures démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - GET /api/factures (toutes les factures)');
  console.log('  - GET /api/factures/:id (facture par ID)');
  console.log('  - GET /api/factures/client/:idClient (factures d\'un client)');
  console.log('  - GET /api/factures/status/:status (factures par statut)');
});