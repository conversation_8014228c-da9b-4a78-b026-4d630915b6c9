// Test simple du serveur
const http = require('http');

function testServer() {
  console.log('🧪 Test simple du serveur sur port 3002...');
  
  const options = {
    hostname: 'localhost',
    port: 3002,
    path: '/',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Serveur répond: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('📡 Réponse:', data);
      testLogin();
    });
  });

  req.on('error', (err) => {
    console.error('❌ Erreur de connexion:', err.message);
    console.log('💡 Le serveur n\'est probablement pas démarré sur le port 3002');
  });

  req.end();
}

function testLogin() {
  console.log('\n🔐 Test de login...');
  
  const postData = JSON.stringify({
    email: '<EMAIL>',
    motDepass: 'Tech123'
  });

  const options = {
    hostname: 'localhost',
    port: 3002,
    path: '/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Login répond: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('🔐 Réponse login:', data);
    });
  });

  req.on('error', (err) => {
    console.error('❌ Erreur de login:', err.message);
  });

  req.write(postData);
  req.end();
}

testServer();
