const http = require('http');

const PORT = 3010;

const server = http.createServer((req, res) => {
  // Configuration CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  console.log(`📡 Requête reçue: ${req.method} ${req.url}`);

  if (req.url === '/') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      message: 'Serveur minimal fonctionnel',
      timestamp: new Date().toISOString(),
      status: 'OK'
    }));
  } else if (req.url === '/api/table/client') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: [
        { idclient: 1, nom: 'Test', prenom: 'Client', adresse: 'Test Address' }
      ]
    }));
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Route non trouvée' }));
  }
});

server.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 Serveur minimal démarré sur http://127.0.0.1:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET  / (test)');
  console.log('  - GET  /api/table/client (test clients)');
});

server.on('error', (err) => {
  console.error('❌ Erreur serveur:', err);
});
