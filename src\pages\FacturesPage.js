import React, { useState, useEffect } from 'react';
import '../TechnicianDashboard.css';

const FacturesPage = ({ onBack }) => {
  const [factures, setFactures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, payée, nonpayée
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [consommationsSansFacture, setConsommationsSansFacture] = useState([]);

  // Configuration de l'API - Serveur de factures sur le port 3004
  const API_BASE_URL = 'http://localhost:3004';

  useEffect(() => {
    fetchFactures();
  }, []);

  const fetchFactures = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('📋 Récupération des factures depuis la base Facutration...');

      const response = await fetch(`${API_BASE_URL}/api/factures`);
      const data = await response.json();

      if (data.success) {
        // Transformer les données pour correspondre au format attendu par l'interface
        const transformedFactures = data.data.map(facture => ({
          id: facture.idfact,
          numero: `FAC-${new Date(facture.date).getFullYear()}-${String(facture.idfact).padStart(3, '0')}`,
          clientId: facture.idclient,
          clientNom: `${facture.nom} ${facture.prenom}`,
          ville: facture.ville,
          montant: parseFloat(facture.montant) || 0,
          date: new Date(facture.date).toLocaleDateString('fr-FR'),
          dateEcheance: facture.periode,
          statut: facture.status === 'payée' ? 'Payée' : 'En attente',
          periode: facture.periode,
          reference: facture.reference,
          idconst: facture.idconst,
          idcont: facture.idcont
        }));

        setFactures(transformedFactures);
        console.log(`✅ ${transformedFactures.length} factures chargées depuis la base Facutration`);
      } else {
        setError(data.error || 'Erreur lors du chargement des factures');
        console.error('❌ Erreur API:', data.error);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des factures:', error);
      setError('Erreur de connexion au serveur de factures');
      // En cas d'erreur, afficher un message mais pas de données mockées
      setFactures([]);
    } finally {
      setLoading(false);
    }
  };


  const filteredFactures = factures.filter(facture => {
    if (filter === 'all') return true;
    if (filter === 'payée') return facture.statut === 'Payée';
    if (filter === 'nonpayée') return facture.statut === 'En attente';
    return true;
  });

  const getStatutColor = (statut) => {
    return statut === 'Payée' ? '#4caf50' : '#ff9800';
  };

  const getTotalMontant = () => {
    return filteredFactures.reduce((total, facture) => total + facture.montant, 0).toFixed(2);
  };

  // Fonction pour télécharger le PDF d'une facture
  const downloadFacturePDF = async (factureId) => {
    try {
      console.log(`📄 Téléchargement du PDF pour la facture ${factureId}...`);

      const response = await fetch(`${API_BASE_URL}/api/factures/${factureId}/pdf`);

      if (!response.ok) {
        throw new Error('Erreur lors du téléchargement du PDF');
      }

      // Créer un blob à partir de la réponse
      const blob = await response.blob();

      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `facture-${factureId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log(`✅ PDF téléchargé pour la facture ${factureId}`);

    } catch (error) {
      console.error('❌ Erreur lors du téléchargement du PDF:', error);
      alert('Erreur lors du téléchargement du PDF');
    }
  };

  // Fonction pour changer le statut d'une facture
  const changerStatutFacture = async (factureId, nouveauStatut) => {
    try {
      console.log(`🔄 Changement de statut pour la facture ${factureId} vers: ${nouveauStatut}`);

      const response = await fetch(`${API_BASE_URL}/api/factures/${factureId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: nouveauStatut })
      });

      const data = await response.json();

      if (data.success) {
        // Recharger les factures pour mettre à jour l'affichage
        await fetchFactures();
        console.log(`✅ Statut mis à jour pour la facture ${factureId}`);
      } else {
        throw new Error(data.error || 'Erreur lors de la mise à jour du statut');
      }

    } catch (error) {
      console.error('❌ Erreur lors du changement de statut:', error);
      alert('Erreur lors du changement de statut');
    }
  };

  // Fonction pour récupérer les consommations sans facture
  const fetchConsommationsSansFacture = async () => {
    try {
      const response = await fetch('http://localhost:3003/api/consommations');
      const data = await response.json();

      if (data.success) {
        // Filtrer les consommations qui n'ont pas encore de facture
        // Pour simplifier, on prend toutes les consommations récentes
        setConsommationsSansFacture(data.data.slice(0, 10));
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des consommations:', error);
    }
  };

  // Fonction pour créer une nouvelle facture
  const creerNouvelleFacture = async (idconst) => {
    try {
      console.log(`💰 Création d'une facture pour la consommation ${idconst}...`);

      const response = await fetch(`${API_BASE_URL}/api/factures`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          idconst: idconst,
          reference: Date.now() // Référence unique basée sur le timestamp
        })
      });

      const data = await response.json();

      if (data.success) {
        alert(`✅ Facture créée avec succès!\nID: ${data.data.idfact}\nMontant: ${data.data.montant.toFixed(2)} DH`);
        // Recharger les factures pour afficher la nouvelle
        await fetchFactures();
        setShowCreateForm(false);
        console.log(`✅ Facture créée avec ID: ${data.data.idfact}`);
      } else {
        throw new Error(data.error || 'Erreur lors de la création de la facture');
      }

    } catch (error) {
      console.error('❌ Erreur lors de la création de la facture:', error);
      alert('Erreur lors de la création de la facture: ' + error.message);
    }
  };

  if (loading) {
    return (
      <div className="tech-mobile-content">
        <div className="tech-mobile-card">
          <div className="tech-mobile-card-header">
            <button onClick={onBack} className="tech-mobile-back-btn">
              ← Retour
            </button>
            <h1 className="tech-mobile-card-title">Factures</h1>
          </div>
          <div className="tech-mobile-loading">
            <div className="tech-mobile-spinner"></div>
            <p>Chargement des factures...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="tech-mobile-content">
      <div className="tech-mobile-card">
        <div className="tech-mobile-card-header">
          <button onClick={onBack} className="tech-mobile-back-btn">
            ← Retour
          </button>
          <div>
            <h1 className="tech-mobile-card-title">Factures</h1>
            <p className="tech-mobile-card-subtitle">
              {filteredFactures.length} facture(s) • Total: {getTotalMontant()} DT
            </p>
          </div>
        </div>
      </div>

      {/* Filtres */}
      <div className="tech-mobile-card">
        <div className="tech-mobile-filter-container">
          <button
            className={`tech-mobile-filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            Toutes ({factures.length})
          </button>
          <button
            className={`tech-mobile-filter-btn ${filter === 'payée' ? 'active' : ''}`}
            onClick={() => setFilter('payée')}
          >
            Payées ({factures.filter(f => f.statut === 'Payée').length})
          </button>
          <button
            className={`tech-mobile-filter-btn ${filter === 'nonpayée' ? 'active' : ''}`}
            onClick={() => setFilter('nonpayée')}
          >
            Non payées ({factures.filter(f => f.statut === 'En attente').length})
          </button>
        </div>


      </div>

      {/* Formulaire de création de facture */}
      {showCreateForm && (
        <div className="tech-mobile-card">
          <h3 style={{ marginBottom: '15px', color: '#4caf50' }}>📄 Créer une nouvelle facture</h3>
          <p style={{ marginBottom: '15px', color: '#666' }}>
            Sélectionnez une consommation pour générer sa facture :
          </p>

          {consommationsSansFacture.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
              <p>Aucune consommation disponible</p>
              <button
                onClick={fetchConsommationsSansFacture}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#2196F3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                🔄 Recharger
              </button>
            </div>
          ) : (
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {consommationsSansFacture.map(consommation => (
                <div
                  key={consommation.idcons}
                  style={{
                    border: '1px solid #ddd',
                    borderRadius: '8px',
                    padding: '15px',
                    marginBottom: '10px',
                    backgroundColor: '#f9f9f9'
                  }}
                >
                  <div style={{ marginBottom: '10px' }}>
                    <strong>Consommation #{consommation.idcons}</strong>
                    <div style={{ fontSize: '14px', color: '#666' }}>
                      Contrat: {consommation.idcont} | Période: {consommation.periode}
                    </div>
                    <div style={{ fontSize: '14px', color: '#666' }}>
                      {consommation.consommationpre} → {consommation.consommationactuelle} m³
                      ({consommation.consommationactuelle - consommation.consommationpre} m³ consommés)
                    </div>
                  </div>
                  <button
                    onClick={() => creerNouvelleFacture(consommation.idcons)}
                    style={{
                      backgroundColor: '#4caf50',
                      color: 'white',
                      border: 'none',
                      padding: '8px 16px',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '14px'
                    }}
                  >
                    💰 Créer Facture
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Affichage des erreurs */}
      {error && (
        <div className="tech-mobile-card" style={{ backgroundColor: '#ffebee', borderLeft: '4px solid #f44336' }}>
          <div style={{ padding: '15px' }}>
            <h4 style={{ color: '#d32f2f', margin: '0 0 10px 0' }}>❌ Erreur</h4>
            <p style={{ color: '#d32f2f', margin: 0 }}>{error}</p>
            <button
              onClick={fetchFactures}
              style={{
                marginTop: '10px',
                padding: '8px 16px',
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              🔄 Réessayer
            </button>
          </div>
        </div>
      )}

      {/* Liste des factures */}
      {!error && filteredFactures.length === 0 ? (
        <div className="tech-mobile-card">
          <div className="tech-mobile-empty-state">
            <div className="tech-mobile-empty-icon">📄</div>
            <h3>Aucune facture trouvée</h3>
            <p>Aucune facture ne correspond aux critères sélectionnés.</p>
          </div>
        </div>
      ) : (
        filteredFactures.map(facture => (
          <div key={facture.id} className="tech-mobile-intervention-item">
            <div className="tech-mobile-intervention-header">
              <div className="tech-mobile-intervention-client">
                <strong>{facture.numero}</strong>
                <div style={{ fontSize: '14px', color: '#6b7280', marginTop: '2px' }}>
                  {facture.clientNom}
                </div>
              </div>
              <div 
                className="tech-mobile-intervention-badge"
                style={{ 
                  backgroundColor: getStatutColor(facture.statut),
                  color: 'white'
                }}
              >
                {facture.statut}
              </div>
            </div>

            <div className="tech-mobile-intervention-details">
              <div className="tech-mobile-intervention-info">
                <span>💰 Montant: {facture.montant} DT</span>
              </div>
              <div className="tech-mobile-intervention-info">
                <span>📅 Date: {new Date(facture.date).toLocaleDateString('fr-FR')}</span>
              </div>
              <div className="tech-mobile-intervention-info">
                <span>⏰ Échéance: {new Date(facture.dateEcheance).toLocaleDateString('fr-FR')}</span>
              </div>
              <div className="tech-mobile-intervention-info">
                <span>📊 Période: {facture.periode}</span>
              </div>
            </div>

            <div className="tech-mobile-intervention-actions">
              <button
                className="tech-mobile-action-btn start"
                onClick={() => downloadFacturePDF(facture.id)}
                style={{ backgroundColor: '#2196F3' }}
              >
                📄 PDF
              </button>

              {facture.statut === 'En attente' && (
                <button
                  className="tech-mobile-action-btn complete"
                  onClick={() => changerStatutFacture(facture.id, 'payée')}
                >
                  ✅ Marquer payée
                </button>
              )}

              {facture.statut === 'Payée' && (
                <button
                  className="tech-mobile-action-btn"
                  onClick={() => changerStatutFacture(facture.id, 'nonpayée')}
                  style={{ backgroundColor: '#ff9800' }}
                >
                  ❌ Marquer non payée
                </button>
              )}

              <button
                className="tech-mobile-action-btn"
                onClick={() => {
                  alert(`Détails de la facture ${facture.numero}\nClient: ${facture.clientNom}\nVille: ${facture.ville || 'N/A'}\nMontant: ${facture.montant} DH\nPériode: ${facture.periode}\nRéférence: ${facture.reference || 'N/A'}`);
                }}
                style={{ backgroundColor: '#9c27b0' }}
              >
                ℹ️ Détails
              </button>
            </div>
          </div>
        ))
      )}
    </div>
  );
};

export default FacturesPage;
