const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 3002;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données Facutration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facutration', // Nom correct avec 'u'
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données
async function testConnection() {
  try {
    const client = await pool.connect();
    console.log('✅ Connexion à la base de données Facutration réussie');
    
    // Vérifier la table utilisateur
    const result = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'utilisateur' AND table_schema = 'public'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 Structure de la table utilisateur:');
    result.rows.forEach(row => {
      console.log(`   - ${row.column_name} (${row.data_type})`);
    });
    
    client.release();
    return true;
  } catch (err) {
    console.error('❌ Erreur de connexion à la base de données:', err.message);
    return false;
  }
}

// Route de test
app.get('/', (req, res) => {
  console.log('📡 Requête reçue sur /');
  res.json({
    message: 'Serveur Facutration fonctionnel',
    timestamp: new Date().toISOString(),
    database: 'Facutration',
    status: 'OK'
  });
});

// Route pour récupérer les clients
app.get('/api/table/client', async (req, res) => {
  try {
    console.log('📡 Requête GET /api/table/client');
    const result = await pool.query('SELECT * FROM client ORDER BY idclient');
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
    
    console.log(`✅ ${result.rows.length} clients récupérés`);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer les utilisateurs (techniciens)
app.get('/api/table/utilisateur', async (req, res) => {
  try {
    console.log('📡 Requête GET /api/table/utilisateur');
    const result = await pool.query('SELECT idtech, nom, prenom, adresse, tel, email, role FROM utilisateur ORDER BY idtech');
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
    
    console.log(`✅ ${result.rows.length} utilisateurs récupérés`);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des utilisateurs',
      error: error.message
    });
  }
});

// Route pour récupérer les consommations
app.get('/api/table/consommation', async (req, res) => {
  try {
    console.log('📡 Requête GET /api/table/consommation');
    const result = await pool.query('SELECT * FROM consommation ORDER BY idcons');
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
    
    console.log(`✅ ${result.rows.length} consommations récupérées`);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des consommations:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des consommations',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats
app.get('/api/table/contract', async (req, res) => {
  try {
    console.log('📡 Requête GET /api/table/contract');
    const result = await pool.query('SELECT * FROM contract ORDER BY idcontract');
    
    res.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
    
    console.log(`✅ ${result.rows.length} contrats récupérés`);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route de login pour les techniciens
app.post('/api/login', async (req, res) => {
  try {
    const { email, motDepass } = req.body;
    console.log('📡 Tentative de connexion pour:', email);
    
    const result = await pool.query(
      'SELECT idtech, nom, prenom, email, role FROM utilisateur WHERE email = $1 AND motdepass = $2',
      [email, motDepass]
    );
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      console.log('✅ Connexion réussie pour:', user.email);
      
      res.json({
        success: true,
        message: 'Connexion réussie',
        user: user
      });
    } else {
      console.log('❌ Échec de connexion pour:', email);
      res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }
  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de la connexion',
      error: error.message
    });
  }
});

// Démarrer le serveur
async function startServer() {
  const dbConnected = await testConnection();
  
  if (!dbConnected) {
    console.log('⚠️ Démarrage du serveur sans connexion à la base de données');
  }
  
  app.listen(PORT, () => {
    console.log(`🚀 Serveur Facutration démarré sur http://localhost:${PORT}`);
    console.log('📊 Base de données: Facutration');
    console.log('📡 Routes disponibles:');
    console.log('  - GET  / (test)');
    console.log('  - GET  /api/table/client');
    console.log('  - GET  /api/table/utilisateur');
    console.log('  - GET  /api/table/consommation');
    console.log('  - GET  /api/table/contract');
    console.log('  - POST /api/login');
  });
}

startServer();

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});
