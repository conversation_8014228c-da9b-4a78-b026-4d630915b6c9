// Service de données offline pour simuler la base Facutration
export class OfflineDataService {
  
  // Données statiques simulant la base Facutration
  static data = {
    // Table utilisateur
    utilisateur: [
      { 
        idtech: 1, 
        nom: 'Technicien', 
        prenom: 'Test', 
        email: '<EMAIL>', 
        motdepass: 'Tech123',
        role: 'Tech', 
        adresse: '123 Rue Tech', 
        tel: '0123456789' 
      },
      { 
        idtech: 2, 
        nom: 'Admin', 
        prenom: 'System', 
        email: '<EMAIL>', 
        motdepass: 'admin123',
        role: 'Admin', 
        adresse: '456 Rue Admin', 
        tel: '0987654321' 
      }
    ],

    // Table client
    client: [
      { 
        idclient: 1, 
        nom: '<PERSON><PERSON>', 
        prenom: '<PERSON>', 
        adresse: '123 Rue de la Paix', 
        ville: 'Paris', 
        tel: '0123456789', 
        email: '<EMAIL>', 
        ids: 1 
      },
      { 
        idclient: 2, 
        nom: '<PERSON>', 
        prenom: '<PERSON>', 
        adresse: '456 Avenue des Champs', 
        ville: 'Lyon', 
        tel: '0987654321', 
        email: '<EMAIL>', 
        ids: 2 
      },
      { 
        idclient: 3, 
        nom: '<PERSON>', 
        prenom: 'Pierre', 
        adresse: '789 Boulevard Central', 
        ville: 'Marseille', 
        tel: '0147258369', 
        email: '<EMAIL>', 
        ids: 1 
      }
    ],

    // Table secteur
    secteur: [
      { ids: 1, nom: 'Centre-Ville' },
      { ids: 2, nom: 'Banlieue Nord' },
      { ids: 3, nom: 'Zone Industrielle' },
      { ids: 4, nom: 'Quartier Résidentiel' }
    ],

    // Table contract
    contract: [
      { 
        idcontract: 1, 
        codeqr: 'QR001', 
        idclient: 1, 
        marquecompteur: 'Sensus', 
        numseriecompteur: 'SN001', 
        datecontract: '2024-01-01', 
        posx: '36.8065', 
        posy: '10.1815' 
      },
      { 
        idcontract: 2, 
        codeqr: 'QR002', 
        idclient: 2, 
        marquecompteur: 'Itron', 
        numseriecompteur: 'SN002', 
        datecontract: '2024-01-15', 
        posx: '36.8075', 
        posy: '10.1825' 
      }
    ],

    // Table consommation
    consommation: [
      { 
        idcons: 1, 
        consommationpre: 150, 
        consommationactuelle: 175, 
        idcont: 1, 
        idtech: 1, 
        idtranch: 1, 
        jours: 30, 
        periode: '2024-01', 
        status: 'validé' 
      },
      { 
        idcons: 2, 
        consommationpre: 200, 
        consommationactuelle: 220, 
        idcont: 2, 
        idtech: 1, 
        idtranch: 2, 
        jours: 30, 
        periode: '2024-01', 
        status: 'validé' 
      }
    ],

    // Table facture
    facture: [
      { 
        idfact: 1, 
        date: '2024-01-31', 
        idconst: 1, 
        montant: 45.50, 
        periode: '2024-01', 
        reference: 202401001, 
        status: 'payée' 
      },
      { 
        idfact: 2, 
        date: '2024-01-31', 
        idconst: 2, 
        montant: 52.30, 
        periode: '2024-01', 
        reference: 202401002, 
        status: 'nonpayée' 
      }
    ],

    // Table tranch
    tranch: [
      { idtranch: 1, prix: 1.20, valeurmin: 0, valeurmax: 20 },
      { idtranch: 2, prix: 1.50, valeurmin: 21, valeurmax: 50 },
      { idtranch: 3, prix: 2.00, valeurmin: 51, valeurmax: 100 },
      { idtranch: 4, prix: 2.50, valeurmin: 101, valeurmax: 999999 }
    ]
  };

  // Simuler un appel API
  static async callAPI(endpoint, options = {}) {
    console.log(`🔄 Mode offline - Simulation API: ${endpoint}`);
    
    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 100));

    // Parser l'endpoint
    const parts = endpoint.split('/');
    
    if (endpoint === '/') {
      return {
        success: true,
        message: 'Serveur Facutration fonctionnel (mode offline)',
        timestamp: new Date().toISOString(),
        database: 'Facutration',
        status: 'OK'
      };
    }

    if (parts[1] === 'api' && parts[2] === 'table') {
      const tableName = parts[3];
      
      if (this.data[tableName]) {
        return {
          success: true,
          data: this.data[tableName],
          count: this.data[tableName].length,
          message: `Données de la table ${tableName} (mode offline)`
        };
      } else {
        return {
          success: false,
          message: `Table ${tableName} non trouvée`,
          data: []
        };
      }
    }

    if (endpoint === '/api/login' && options.method === 'POST') {
      const { email, motDepass } = options.body || {};
      
      const user = this.data.utilisateur.find(u => 
        u.email === email && u.motdepass === motDepass
      );
      
      if (user) {
        const { motdepass, ...userWithoutPassword } = user;
        return {
          success: true,
          message: 'Connexion réussie (mode offline)',
          user: userWithoutPassword
        };
      } else {
        return {
          success: false,
          message: 'Email ou mot de passe incorrect'
        };
      }
    }

    return {
      success: false,
      message: `Endpoint ${endpoint} non supporté en mode offline`,
      data: []
    };
  }

  // Méthodes spécifiques pour chaque table
  static async getClients() {
    return this.callAPI('/api/table/client');
  }

  static async getUtilisateurs() {
    return this.callAPI('/api/table/utilisateur');
  }

  static async getConsommations() {
    return this.callAPI('/api/table/consommation');
  }

  static async getContracts() {
    return this.callAPI('/api/table/contract');
  }

  static async getFactures() {
    return this.callAPI('/api/table/facture');
  }

  static async getSecteurs() {
    return this.callAPI('/api/table/secteur');
  }

  static async getTranches() {
    return this.callAPI('/api/table/tranch');
  }

  static async login(email, motDepass) {
    return this.callAPI('/api/login', {
      method: 'POST',
      body: { email, motDepass }
    });
  }
}
