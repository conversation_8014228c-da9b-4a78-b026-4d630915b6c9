const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3003;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 Démarrage du serveur de login...');

// Route de test
app.get('/', (req, res) => {
  console.log('📡 Route / appelée');
  res.json({
    message: 'Serveur de login fonctionnel',
    timestamp: new Date().toISOString(),
    status: 'OK'
  });
});

// Route de login
app.post('/login', (req, res) => {
  console.log('📥 Requête POST /login:', req.body);
  const { email, motDepass } = req.body;

  // Validation des champs requis
  if (!email || !motDepass) {
    return res.status(400).json({
      success: false,
      message: "Email et mot de passe requis"
    });
  }

  // Test avec les identifiants du technicien
  if (email === '<EMAIL>' && motDepass === 'Tech123') {
    console.log('✅ Connexion réussie pour le technicien');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: 1,
        nom: 'Technicien',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Tech'
      }
    });
  }

  // Test avec les identifiants admin
  if (email === '<EMAIL>' && motDepass === 'admin123') {
    console.log('✅ Connexion réussie pour l\'admin');
    return res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: 2,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      }
    });
  }

  // Identifiants incorrects
  console.log('❌ Identifiants incorrects:', { email, motDepass });
  return res.status(401).json({
    success: false,
    message: 'Email ou mot de passe incorrect'
  });
});

// Route pour récupérer la dernière consommation (pour la page consommation)
app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 GET /api/last-consommation-global');
  res.json({
    success: true,
    message: 'Dernière consommation récupérée avec succès',
    data: {
      idcons: 18,
      consommationpre: 122,
      consommationactuelle: 135,
      idcont: 10,
      idtech: 1,
      idtranch: 1,
      jours: 30,
      periode: "juin 2025",
      status: "nouveau"
    }
  });
});

// Route pour récupérer les contrats
app.get('/api/contracts', (req, res) => {
  console.log('📥 GET /api/contracts');
  res.json({
    success: true,
    message: 'Contrats récupérés avec succès',
    count: 1,
    data: [{
      idcontract: 10,
      codeqr: "QR123",
      datecontract: "2024-01-15T09:00:00.000Z",
      idclient: 3,
      marquecompteur: "Sagemcom",
      nom: "Dupont",
      prenom: "Jean",
      ville: "Paris"
    }]
  });
});

// Route POST pour enregistrer une consommation
app.post('/api/consommations', (req, res) => {
  console.log('📝 POST /api/consommations');
  console.log('Données reçues:', req.body);
  
  const newConsommation = {
    idcons: Math.floor(Math.random() * 1000) + 100,
    consommationpre: req.body.consommationpre || 0,
    consommationactuelle: req.body.consommationactuelle,
    idcont: req.body.idcont,
    idtech: req.body.idtech || 1,
    idtranch: 1,
    jours: req.body.jours || 30,
    periode: req.body.periode || new Date().toISOString().slice(0, 7),
    status: 'nouveau',
    timestamp: new Date().toISOString()
  };
  
  console.log('✅ Consommation simulée:', newConsommation);
  
  res.json({
    success: true,
    message: 'Consommation enregistrée avec succès',
    data: newConsommation
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur de login démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('  - GET / (test)');
  console.log('  - POST /login (authentification)');
  console.log('  - GET /api/last-consommation-global');
  console.log('  - GET /api/contracts');
  console.log('  - POST /api/consommations');
  console.log('🔑 Comptes disponibles:');
  console.log('  - <EMAIL> / Tech123 (Technicien)');
  console.log('  - <EMAIL> / admin123 (Admin)');
  console.log('🎯 Le serveur est prêt !');
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
