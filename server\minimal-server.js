const express = require('express');
const cors = require('cors');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Route de test
app.get('/', (req, res) => {
  console.log('📥 Requête GET /');
  res.json({ 
    message: 'Serveur Facturation fonctionnel', 
    timestamp: new Date().toISOString() 
  });
});

// Route de test pour login (sans base de données)
app.post('/login', (req, res) => {
  console.log('📥 Requête POST /login:', req.body);
  const { email, motDepass } = req.body;

  // Test simple
  if (email === '<EMAIL>' && motDepass === 'admin123') {
    res.json({
      success: true,
      message: 'Connexion réussie (test)',
      user: {
        id: 1,
        nom: 'Admin',
        prenom: 'Test',
        email: '<EMAIL>',
        role: 'Admin'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Email ou mot de passe incorrect'
    });
  }
});

// Route pour récupérer la dernière consommation globale (simulation)
app.get('/api/last-consommation-global', (req, res) => {
  console.log('📥 Récupération de la dernière consommation globale...');

  // Simulation de la dernière consommation de votre base de données
  res.json({
    success: true,
    message: 'Dernière consommation globale récupérée avec succès',
    data: {
      idcons: 14,
      consommationpre: 150,
      consommationactuelle: 122, // Dernière consommation de votre base
      idcont: 10,
      idtech: 1,
      idtranch: 1,
      jours: 30,
      periode: "juin 2025",
      status: "actif"
    }
  });
});

// Route pour récupérer les contrats (simulation)
app.get('/api/contracts', (req, res) => {
  console.log('📥 Récupération des contrats...');

  res.json({
    success: true,
    message: 'Contrats récupérés avec succès',
    count: 1,
    data: [{
      idcontract: 10,
      codeqr: "QR123",
      datecontract: "2024-01-15T09:00:00.000Z",
      marquecompteur: "Sagemcom",
      nom: "Dupont",
      prenom: "Jean",
      ville: "Paris"
    }]
  });
});

// Route POST pour enregistrer une consommation
app.post('/api/consommations', (req, res) => {
  console.log('📝 Enregistrement d\'une nouvelle consommation...');
  console.log('Données reçues:', req.body);

  // Simulation d'un succès
  res.json({
    success: true,
    message: 'Consommation enregistrée avec succès',
    data: {
      idcons: Math.floor(Math.random() * 1000) + 100,
      ...req.body,
      timestamp: new Date().toISOString()
    }
  });
});

// Démarrer le serveur
const PORT = 3006; // Utiliser le port 3006
console.log(`🔧 Démarrage du serveur sur le port ${PORT}...`);

const server = app.listen(PORT, () => {
  console.log(`✅ Serveur minimal démarré sur http://localhost:${PORT}`);
});

server.on('error', (error) => {
  console.error('❌ Erreur du serveur:', error);
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  console.error('❌ Exception non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée non gérée:', reason);
});
