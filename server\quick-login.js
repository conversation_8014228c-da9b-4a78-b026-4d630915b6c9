console.log('🚀 Démarrage du serveur quick-login...');

try {
  const express = require('express');
  const cors = require('cors');
  
  const app = express();
  const PORT = 3003;
  
  app.use(cors());
  app.use(express.json());
  
  console.log('✅ Middleware configuré');
  
  // Route de test
  app.get('/', (req, res) => {
    console.log('📡 GET /');
    res.json({ message: 'Serveur quick-login OK', timestamp: new Date().toISOString() });
  });
  
  // Route de login
  app.post('/login', (req, res) => {
    console.log('📥 POST /login:', req.body);
    const { email, motDepass } = req.body;
    
    if (email === '<EMAIL>' && motDepass === 'Tech123') {
      res.json({
        success: true,
        message: 'Connexion réussie',
        user: {
          id: 1,
          nom: 'Technicien',
          prenom: 'Test',
          email: '<EMAIL>',
          role: 'Tech'
        }
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }
  });
  
  // Routes API pour la consommation
  app.get('/api/last-consommation-global', (req, res) => {
    console.log('📥 GET /api/last-consommation-global');
    res.json({
      success: true,
      data: { consommationactuelle: 135 }
    });
  });
  
  app.get('/api/contracts', (req, res) => {
    console.log('📥 GET /api/contracts');
    res.json({
      success: true,
      data: [{ idcontract: 10, nom: "Dupont", prenom: "Jean" }]
    });
  });
  
  app.post('/api/consommations', (req, res) => {
    console.log('📝 POST /api/consommations:', req.body);
    res.json({
      success: true,
      message: 'Consommation enregistrée avec succès',
      data: { id: 123, ...req.body }
    });
  });
  
  console.log('✅ Routes configurées');
  
  app.listen(PORT, () => {
    console.log(`✅ Serveur quick-login démarré sur http://localhost:${PORT}`);
    console.log('🔑 Utilisez: <EMAIL> / Tech123');
  });
  
} catch (error) {
  console.error('❌ Erreur:', error);
}
